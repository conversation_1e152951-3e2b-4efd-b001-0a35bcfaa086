C51 COMPILER V9.01   MAIN                                                                  07/20/2025 12:44:14 PAGE 1   


C51 COMPILER V9.01, COMPILATION OF MODULE MAIN
OBJECT MODULE PLACED IN .\Objects\main.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\Source\main.c BROWSE INCDIR(..\Source) DEBUG OBJECTEXTEND PRINT(.\Listin
                    -gs\main.lst) OBJECT(.\Objects\main.obj)

line level    source

*** WARNING C500 IN LINE 1 OF ..\SOURCE\MAIN.C: LICENSE ERROR (R208: RENEW LICENSE ID CODE (LIC))

   1          /*******************************************************************************
   2           * 激光雕刻机控制系统 - 单片机主程序
   3           * 目标芯片: STC15W201S
   4           * 开发环境: Keil uVision 5
   5           * 版本: v1.0.0
   6           * 日期: 2025-07-20
   7           ******************************************************************************/
   8          
   9          #include "STC15W201S.h"
  10          #include "uart.h"
*** ERROR C141 IN LINE 23 OF ..\SOURCE\UART.H: syntax error near ','
  11          #include "stepper.h"
  12          #include "laser.h"
  13          #include "protocol.h"
*** ERROR C141 IN LINE 64 OF ..\SOURCE\PROTOCOL.H: syntax error near ','
  14          #include "system.h"
  15          
  16          /*******************************************************************************
  17           * 全局变量定义
  18           ******************************************************************************/
  19          volatile uint8_t system_state = SYS_IDLE;    // 系统状态
  20          volatile uint16_t current_x = 0;             // 当前X坐标
  21          volatile uint16_t current_y = 0;             // 当前Y坐标
  22          volatile uint8_t laser_power = 0;            // 激光功率
  23          volatile uint8_t move_speed = 50;            // 移动速度
  24          
  25          /*******************************************************************************
  26           * 主函数
  27           ******************************************************************************/
  28          void main(void)
  29          {
  30   1          // 系统初始化
  31   1          System_Init();
  32   1          UART_Init();
  33   1          Stepper_Init();
  34   1          Laser_Init();
  35   1          Protocol_Init();
  36   1          
  37   1          // 使能全局中断
  38   1          EA = 1;
  39   1          
  40   1          // 发送启动信息
  41   1          UART_SendString("Laser Engraver Ready\r\n");
  42   1          
  43   1          while(1)
  44   1          {
  45   2              // 处理串口接收的命令
  46   2              Protocol_ProcessCommand();
  47   2              
  48   2              // 执行步进电机运动
  49   2              Stepper_Process();
  50   2              
  51   2              // 更新激光状态
C51 COMPILER V9.01   MAIN                                                                  07/20/2025 12:44:14 PAGE 2   

  52   2              Laser_Process();
  53   2              
  54   2              // 系统状态监控
  55   2              System_Monitor();
  56   2              
  57   2              // 看门狗喂狗
  58   2              System_WatchdogFeed();
  59   2          }
  60   1      }
  61          
  62          /*******************************************************************************
  63           * 定时器0中断服务程序 - 用于步进电机脉冲生成
  64           ******************************************************************************/
  65          void Timer0_ISR(void) interrupt 1
  66          {
  67   1          TH0 = 0xFC;  // 重装载定时器值 (1ms @ 12MHz)
  68   1          TL0 = 0x18;
  69   1          
  70   1          // 步进电机脉冲生成
  71   1          Stepper_TimerISR();
  72   1          
  73   1          // 激光PWM更新
  74   1          Laser_TimerISR();
  75   1      }
  76          
  77          /*******************************************************************************
  78           * 串口中断服务程序
  79           ******************************************************************************/
  80          void UART_ISR(void) interrupt 4
  81          {
  82   1          if(RI)  // 接收中断
  83   1          {
  84   2              RI = 0;
  85   2              UART_ReceiveISR();
  86   2          }
  87   1          
  88   1          if(TI)  // 发送中断
  89   1          {
  90   2              TI = 0;
  91   2              UART_TransmitISR();
  92   2          }
  93   1      }

C51 COMPILATION COMPLETE.  1 WARNING(S),  2 ERROR(S)
