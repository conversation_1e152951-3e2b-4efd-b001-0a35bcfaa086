/*******************************************************************************
 * 激光控制器类
 * 功能: 控制激光器的开关和功率
 ******************************************************************************/

using System;

namespace LaserEngraverControl
{
    /// <summary>
    /// 激光控制器类
    /// </summary>
    public class LaserController
    {
        #region 私有字段
        private SerialCommunication serialComm;
        private int currentPower = 0;
        private bool isLaserOn = false;
        #endregion

        #region 事件定义
        public event EventHandler<string> StatusChanged;
        public event EventHandler<int> PowerChanged;
        #endregion

        #region 属性
        /// <summary>
        /// 当前激光功率 (0-100)
        /// </summary>
        public int CurrentPower
        {
            get { return currentPower; }
        }

        /// <summary>
        /// 激光是否开启
        /// </summary>
        public bool IsLaserOn
        {
            get { return isLaserOn; }
        }
        #endregion

        #region 构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serialCommunication">串口通信对象</param>
        public LaserController(SerialCommunication serialCommunication)
        {
            serialComm = serialCommunication ?? throw new ArgumentNullException(nameof(serialCommunication));
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 设置激光功率
        /// </summary>
        /// <param name="power">功率百分比 (0-100)</param>
        /// <returns>设置是否成功</returns>
        public bool SetPower(int power)
        {
            // 限制功率范围
            if (power < 0) power = 0;
            if (power > 100) power = 100;

            try
            {
                // 发送激光功率设置命令
                bool success = serialComm.SendCommand('L', power.ToString());
                
                if (success)
                {
                    currentPower = power;
                    isLaserOn = power > 0;
                    PowerChanged?.Invoke(this, power);
                    StatusChanged?.Invoke(this, $"激光功率设置为 {power}%");
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"设置激光功率失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 开启激光
        /// </summary>
        /// <param name="power">功率百分比 (1-100)</param>
        /// <returns>开启是否成功</returns>
        public bool TurnOn(int power = 50)
        {
            if (power <= 0) power = 1;
            if (power > 100) power = 100;

            return SetPower(power);
        }

        /// <summary>
        /// 关闭激光
        /// </summary>
        /// <returns>关闭是否成功</returns>
        public bool TurnOff()
        {
            try
            {
                bool success = serialComm.SendCommand('O');
                
                if (success)
                {
                    currentPower = 0;
                    isLaserOn = false;
                    PowerChanged?.Invoke(this, 0);
                    StatusChanged?.Invoke(this, "激光已关闭");
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"关闭激光失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 切换激光状态
        /// </summary>
        /// <param name="power">开启时的功率</param>
        /// <returns>操作是否成功</returns>
        public bool Toggle(int power = 50)
        {
            if (isLaserOn)
            {
                return TurnOff();
            }
            else
            {
                return TurnOn(power);
            }
        }

        /// <summary>
        /// 获取激光状态信息
        /// </summary>
        /// <returns>状态信息字符串</returns>
        public string GetStatusInfo()
        {
            return $"激光状态: {(isLaserOn ? "开启" : "关闭")}, 功率: {currentPower}%";
        }

        /// <summary>
        /// 验证功率值是否有效
        /// </summary>
        /// <param name="power">功率值</param>
        /// <returns>是否有效</returns>
        public static bool IsValidPower(int power)
        {
            return power >= 0 && power <= 100;
        }

        /// <summary>
        /// 紧急停止激光
        /// </summary>
        /// <returns>停止是否成功</returns>
        public bool EmergencyStop()
        {
            try
            {
                // 立即关闭激光
                bool success = TurnOff();
                
                if (success)
                {
                    StatusChanged?.Invoke(this, "激光紧急停止");
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"激光紧急停止失败: {ex.Message}");
                return false;
            }
        }
        #endregion
    }
}
