"""
数据库模型定义
"""

from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db
import json

class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    is_admin = db.Column(db.<PERSON>, default=False)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # 关系
    upload_files = db.relationship('UploadFile', backref='user', lazy='dynamic')
    processing_records = db.relationship('ProcessingRecord', backref='user', lazy='dynamic')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_admin': self.is_admin,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.username}>'


class UploadFile(db.Model):
    """上传文件模型"""
    __tablename__ = 'upload_files'
    
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)
    file_type = db.Column(db.String(50), nullable=False)
    upload_time = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # 文件状态
    status = db.Column(db.String(20), default='uploaded')  # uploaded, processing, completed, failed
    
    # 关系
    processing_records = db.relationship('ProcessingRecord', backref='upload_file', lazy='dynamic')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'upload_time': self.upload_time.isoformat() if self.upload_time else None,
            'status': self.status,
            'user_id': self.user_id
        }
    
    def __repr__(self):
        return f'<UploadFile {self.original_filename}>'


class ProcessingRecord(db.Model):
    """数据处理记录模型"""
    __tablename__ = 'processing_records'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.String(100), unique=True, nullable=False, index=True)
    processing_type = db.Column(db.String(50), nullable=False)  # analysis, prediction, conversion
    status = db.Column(db.String(20), default='pending')  # pending, running, completed, failed
    progress = db.Column(db.Integer, default=0)  # 0-100
    
    # 时间信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    
    # 处理参数和结果
    parameters = db.Column(db.Text)  # JSON格式的处理参数
    results = db.Column(db.Text)     # JSON格式的处理结果
    error_message = db.Column(db.Text)
    
    # 外键
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    file_id = db.Column(db.Integer, db.ForeignKey('upload_files.id'), nullable=False)
    
    def set_parameters(self, params_dict):
        """设置处理参数"""
        self.parameters = json.dumps(params_dict, ensure_ascii=False)
    
    def get_parameters(self):
        """获取处理参数"""
        if self.parameters:
            return json.loads(self.parameters)
        return {}
    
    def set_results(self, results_dict):
        """设置处理结果"""
        self.results = json.dumps(results_dict, ensure_ascii=False)
    
    def get_results(self):
        """获取处理结果"""
        if self.results:
            return json.loads(self.results)
        return {}
    
    @property
    def duration(self):
        """计算处理时长"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'processing_type': self.processing_type,
            'status': self.status,
            'progress': self.progress,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'duration': self.duration,
            'parameters': self.get_parameters(),
            'results': self.get_results(),
            'error_message': self.error_message,
            'user_id': self.user_id,
            'file_id': self.file_id
        }
    
    def __repr__(self):
        return f'<ProcessingRecord {self.task_id}>'


class BatteryData(db.Model):
    """电池测试数据模型"""
    __tablename__ = 'battery_data'
    
    id = db.Column(db.Integer, primary_key=True)
    batch_number = db.Column(db.String(100), nullable=False, index=True)
    channel_number = db.Column(db.Integer, nullable=False)
    battery_code = db.Column(db.String(100), nullable=False, index=True)
    
    # 测试时间
    test_start_time = db.Column(db.DateTime, nullable=False)
    test_end_time = db.Column(db.DateTime, nullable=False)
    test_duration = db.Column(db.Integer, nullable=False)  # 秒
    
    # 测试数据
    voltage = db.Column(db.Float, nullable=False)
    rs_impedance = db.Column(db.Float, nullable=False)  # mΩ
    rct_impedance = db.Column(db.Float, nullable=False)  # mΩ
    w_impedance = db.Column(db.Float, nullable=False)   # mΩ
    
    # 档位信息
    rs_range = db.Column(db.String(50))
    rct_range = db.Column(db.String(50))
    voltage_range = db.Column(db.String(50))
    
    # 测试结果
    outlier_rate = db.Column(db.Float)  # 离群率
    test_result = db.Column(db.String(20), nullable=False)  # 合格/不合格
    failure_reason = db.Column(db.String(200))
    
    # 其他信息
    operator = db.Column(db.String(50))
    battery_type = db.Column(db.String(50))
    battery_spec = db.Column(db.String(100))
    
    # 关联文件
    file_id = db.Column(db.Integer, db.ForeignKey('upload_files.id'), nullable=False)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'batch_number': self.batch_number,
            'channel_number': self.channel_number,
            'battery_code': self.battery_code,
            'test_start_time': self.test_start_time.isoformat() if self.test_start_time else None,
            'test_end_time': self.test_end_time.isoformat() if self.test_end_time else None,
            'test_duration': self.test_duration,
            'voltage': self.voltage,
            'rs_impedance': self.rs_impedance,
            'rct_impedance': self.rct_impedance,
            'w_impedance': self.w_impedance,
            'rs_range': self.rs_range,
            'rct_range': self.rct_range,
            'voltage_range': self.voltage_range,
            'outlier_rate': self.outlier_rate,
            'test_result': self.test_result,
            'failure_reason': self.failure_reason,
            'operator': self.operator,
            'battery_type': self.battery_type,
            'battery_spec': self.battery_spec,
            'file_id': self.file_id
        }
    
    def __repr__(self):
        return f'<BatteryData {self.battery_code}>'
