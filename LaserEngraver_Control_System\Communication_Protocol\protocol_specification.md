# 激光雕刻机通信协议规范

## 1. 协议概述

### 1.1 基本参数
- **波特率**: 9600 bps
- **数据位**: 8位
- **停止位**: 1位
- **校验位**: 无
- **流控制**: 无

### 1.2 数据格式
所有命令采用ASCII格式传输，以便于调试和扩展。

## 2. 命令格式

### 2.1 基本格式
```
<STX><CMD><DATA><ETX><CHK>
```

- **STX**: 起始字符 (0x02)
- **CMD**: 命令字符 (1字节)
- **DATA**: 数据部分 (可变长度)
- **ETX**: 结束字符 (0x03)
- **CHK**: 校验和 (2字节十六进制)

### 2.2 校验和计算
校验和为CMD和DATA部分所有字节的异或运算结果。

## 3. 命令定义

### 3.1 运动控制命令

#### 3.1.1 移动到指定位置 (M)
```
<STX>M<X坐标>,<Y坐标><ETX><CHK>
```
示例: `<STX>M100,200<ETX>A5`

#### 3.1.2 相对移动 (R)
```
<STX>R<X偏移>,<Y偏移><ETX><CHK>
```
示例: `<STX>R10,-5<ETX>B2`

#### 3.1.3 设置速度 (V)
```
<STX>V<速度值><ETX><CHK>
```
速度值范围: 1-100 (百分比)
示例: `<STX>V50<ETX>C1`

### 3.2 激光控制命令

#### 3.2.1 激光开启 (L)
```
<STX>L<功率值><ETX><CHK>
```
功率值范围: 0-100 (百分比)
示例: `<STX>L80<ETX>D3`

#### 3.2.2 激光关闭 (O)
```
<STX>O<ETX><CHK>
```
示例: `<STX>O<ETX>E4`

### 3.3 系统控制命令

#### 3.3.1 复位 (Z)
```
<STX>Z<ETX><CHK>
```
示例: `<STX>Z<ETX>F5`

#### 3.3.2 暂停 (P)
```
<STX>P<ETX><CHK>
```
示例: `<STX>P<ETX>G6`

#### 3.3.3 恢复 (C)
```
<STX>C<ETX><CHK>
```
示例: `<STX>C<ETX>H7`

#### 3.3.4 停止 (S)
```
<STX>S<ETX><CHK>
```
示例: `<STX>S<ETX>I8`

### 3.4 状态查询命令

#### 3.4.1 查询位置 (Q)
```
<STX>Q<ETX><CHK>
```
示例: `<STX>Q<ETX>J9`

#### 3.4.2 查询状态 (T)
```
<STX>T<ETX><CHK>
```
示例: `<STX>T<ETX>K0`

## 4. 响应格式

### 4.1 成功响应
```
<STX>OK<DATA><ETX><CHK>
```

### 4.2 错误响应
```
<STX>ERR<错误码><ETX><CHK>
```

### 4.3 状态响应
```
<STX>STA<状态数据><ETX><CHK>
```

## 5. 错误码定义

| 错误码 | 说明 |
|--------|------|
| 01 | 命令格式错误 |
| 02 | 校验和错误 |
| 03 | 参数超出范围 |
| 04 | 系统忙碌 |
| 05 | 硬件故障 |
| 06 | 未知命令 |

## 6. 重传机制

### 6.1 超时设置
- 命令超时: 1000ms
- 重传次数: 3次

### 6.2 重传流程
1. 发送命令
2. 等待响应
3. 超时或错误时重传
4. 达到最大重传次数后报告失败

## 7. 状态数据格式

### 7.1 位置状态
```
X:<X坐标>,Y:<Y坐标>
```

### 7.2 系统状态
```
STA:<状态>,PWR:<功率>,SPD:<速度>
```

状态值:
- 0: 空闲
- 1: 运行
- 2: 暂停
- 3: 错误
