C51 COMPILER V9.01   LASER                                                                 07/20/2025 12:44:14 PAGE 1   


C51 COMPILER V9.01, COMPILATION OF MODULE LASER
OBJECT MODULE PLACED IN .\Objects\laser.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\Source\laser.c BROWSE INCDIR(..\Source) DEBUG OBJECTEXTEND PRINT(.\Listi
                    -ngs\laser.lst) OBJECT(.\Objects\laser.obj)

line level    source

*** WARNING C500 IN LINE 1 OF ..\SOURCE\LASER.C: LICENSE ERROR (R208: RENEW LICENSE ID CODE (LIC))

   1          /*******************************************************************************
   2           * 激光控制模块实现
   3           * 功能: 控制激光器的开关和功率
   4           ******************************************************************************/
   5          
   6          #include "laser.h"
   7          
   8          /*******************************************************************************
   9           * 全局变量定义
  10           ******************************************************************************/
  11          volatile uint8_t laser_power = 0;          // 当前激光功率 (0-100)
  12          volatile uint8_t laser_state = LASER_OFF;  // 激光状态
  13          volatile uint8_t laser_pwm_counter = 0;    // PWM计数器
  14          
  15          /*******************************************************************************
  16           * 函数: Laser_Init
  17           * 功能: 激光器初始化
  18           * 参数: 无
  19           * 返回: 无
  20           ******************************************************************************/
  21          void Laser_Init(void)
  22          {
  23   1          // 初始化激光控制引脚
  24   1          LASER_PWM = 0;  // PWM输出初始为低电平
  25   1          LASER_EN = 0;   // 激光使能初始为关闭
  26   1          
  27   1          // 初始化变量
  28   1          laser_power = 0;
  29   1          laser_state = LASER_OFF;
  30   1          laser_pwm_counter = 0;
  31   1      }
  32          
  33          /*******************************************************************************
  34           * 函数: Laser_SetPower
  35           * 功能: 设置激光功率
  36           * 参数: power - 功率百分比 (0-100)
  37           * 返回: 无
  38           ******************************************************************************/
  39          void Laser_SetPower(uint8_t power)
  40          {
  41   1          // 限制功率范围
  42   1          if(power > LASER_MAX_POWER)
  43   1              power = LASER_MAX_POWER;
  44   1          
  45   1          laser_power = power;
  46   1          
  47   1          // 如果功率为0，关闭激光
  48   1          if(power == 0)
  49   1          {
  50   2              Laser_Off();
  51   2          }
  52   1          else if(laser_state == LASER_OFF)
  53   1          {
C51 COMPILER V9.01   LASER                                                                 07/20/2025 12:44:14 PAGE 2   

  54   2              // 如果激光处于关闭状态但设置了功率，则开启激光
  55   2              Laser_On();
  56   2          }
  57   1      }
  58          
  59          /*******************************************************************************
  60           * 函数: Laser_On
  61           * 功能: 开启激光
  62           * 参数: 无
  63           * 返回: 无
  64           ******************************************************************************/
  65          void Laser_On(void)
  66          {
  67   1          if(laser_power > 0)
  68   1          {
  69   2              laser_state = LASER_ON;
  70   2              LASER_EN = 1;  // 使能激光器
  71   2          }
  72   1      }
  73          
  74          /*******************************************************************************
  75           * 函数: Laser_Off
  76           * 功能: 关闭激光
  77           * 参数: 无
  78           * 返回: 无
  79           ******************************************************************************/
  80          void Laser_Off(void)
  81          {
  82   1          laser_state = LASER_OFF;
  83   1          LASER_EN = 0;    // 禁用激光器
  84   1          LASER_PWM = 0;   // PWM输出为低电平
  85   1      }
  86          
  87          /*******************************************************************************
  88           * 函数: Laser_GetPower
  89           * 功能: 获取当前激光功率
  90           * 参数: 无
  91           * 返回: 当前功率百分比
  92           ******************************************************************************/
  93          uint8_t Laser_GetPower(void)
  94          {
  95   1          return laser_power;
  96   1      }
  97          
  98          /*******************************************************************************
  99           * 函数: Laser_GetState
 100           * 功能: 获取激光状态
 101           * 参数: 无
 102           * 返回: 激光状态 (LASER_ON/LASER_OFF)
 103           ******************************************************************************/
 104          uint8_t Laser_GetState(void)
 105          {
 106   1          return laser_state;
 107   1      }
 108          
 109          /*******************************************************************************
 110           * 函数: Laser_Process
 111           * 功能: 激光处理函数 (主循环调用)
 112           * 参数: 无
 113           * 返回: 无
 114           ******************************************************************************/
 115          void Laser_Process(void)
C51 COMPILER V9.01   LASER                                                                 07/20/2025 12:44:14 PAGE 3   

 116          {
 117   1          // 安全检查 - 如果系统处于错误状态，关闭激光
 118   1          extern volatile uint8_t system_state;
 119   1          if(system_state == SYS_ERROR)
 120   1          {
 121   2              Laser_Off();
 122   2          }
 123   1      }
 124          
 125          /*******************************************************************************
 126           * 函数: Laser_TimerISR
 127           * 功能: 定时器中断服务程序 - 生成PWM信号
 128           * 参数: 无
 129           * 返回: 无
 130           ******************************************************************************/
 131          void Laser_TimerISR(void)
 132          {
 133   1          // PWM信号生成 (软件PWM)
 134   1          if(laser_state == LASER_ON)
 135   1          {
 136   2              laser_pwm_counter++;
 137   2              
 138   2              // PWM周期为100个计数 (对应100%功率)
 139   2              if(laser_pwm_counter >= PWM_RESOLUTION)
 140   2              {
 141   3                  laser_pwm_counter = 0;
 142   3              }
 143   2              
 144   2              // 根据功率设置PWM输出
 145   2              if(laser_pwm_counter < laser_power)
 146   2              {
 147   3                  LASER_PWM = 1;  // 高电平
 148   3              }
 149   2              else
 150   2              {
 151   3                  LASER_PWM = 0;  // 低电平
 152   3              }
 153   2          }
 154   1          else
 155   1          {
 156   2              LASER_PWM = 0;      // 激光关闭时PWM为低电平
 157   2              laser_pwm_counter = 0;
 158   2          }
 159   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    108    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =      3    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  1 WARNING(S),  0 ERROR(S)
