# 电池数据云处理平台

## 项目概述

这是一个基于Web的电池测试数据处理平台，支持Excel文件上传、数据分析、统计报告生成和机器学习预测。

## 功能特性

### 核心功能
- ✅ Excel文件上传和解析
- ✅ 电池测试数据统计分析
- ✅ 数据趋势分析和可视化
- ✅ 异常数据检测
- ✅ 数据格式转换
- ✅ 机器学习预测模型
- ✅ 批量文件处理
- ✅ 处理进度实时显示

### 用户管理
- ✅ 用户注册/登录
- ✅ 处理历史记录
- ✅ 数据权限管理

### API接口
- ✅ RESTful API设计
- ✅ 文件上传接口
- ✅ 数据查询接口
- ✅ 批量处理接口

## 技术栈

### 后端
- **Python 3.8+**
- **Flask** - Web框架
- **SQLite** - 数据库
- **pandas** - 数据处理
- **openpyxl** - Excel文件处理
- **numpy** - 数值计算
- **scikit-learn** - 机器学习
- **Celery** - 异步任务处理

### 前端
- **Bootstrap 5** - UI框架
- **JavaScript ES6+** - 前端逻辑
- **Chart.js** - 数据可视化
- **jQuery** - DOM操作

### 部署
- **Gunicorn** - WSGI服务器
- **Nginx** - 反向代理

## 项目结构

```
battery_data_platform/
├── app/
│   ├── __init__.py
│   ├── models.py          # 数据库模型
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── auth.py        # 用户认证
│   │   ├── upload.py      # 文件上传
│   │   ├── analysis.py    # 数据分析
│   │   └── api.py         # API接口
│   ├── services/
│   │   ├── __init__.py
│   │   ├── data_processor.py  # 数据处理服务
│   │   ├── ml_predictor.py    # 机器学习预测
│   │   └── file_handler.py    # 文件处理服务
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── uploads/
│   └── templates/
│       ├── base.html
│       ├── index.html
│       ├── upload.html
│       ├── results.html
│       └── history.html
├── config.py              # 配置文件
├── requirements.txt       # 依赖包
├── run.py                # 启动文件
├── deploy/
│   ├── nginx.conf         # Nginx配置
│   ├── gunicorn.conf.py   # Gunicorn配置
│   └── systemd.service    # 系统服务配置
└── docs/
    ├── api.md             # API文档
    ├── deployment.md      # 部署指南
    └── user_guide.md      # 用户手册
```

## 数据处理能力

### 支持的数据类型
- Excel文件 (.xlsx, .xls)
- CSV文件
- 电池测试数据格式

### 分析功能
1. **基础统计分析**
   - 电压、阻抗统计
   - 测试时长分析
   - 合格率统计

2. **趋势分析**
   - 时间序列分析
   - 批次对比分析
   - 通道性能分析

3. **异常检测**
   - 离群值检测
   - 异常模式识别
   - 质量预警

4. **机器学习预测**
   - 电池性能预测
   - 故障预测
   - 寿命评估

## 快速开始

### 环境要求
- Python 3.8+
- 8GB+ RAM
- 100GB+ 存储空间

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd battery_data_platform
```

2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows
```

3. 安装依赖
```bash
pip install -r requirements.txt
```

4. 初始化数据库
```bash
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
```

5. 启动应用
```bash
python run.py
```

6. 访问应用
打开浏览器访问 `http://localhost:5000`

## 部署指南

详细的部署指南请参考 [deployment.md](docs/deployment.md)

### 网络配置要点
1. 路由器端口转发：80 -> 5000
2. 防火墙开放端口：80, 443
3. 域名解析配置
4. SSL证书配置（推荐）

## API文档

详细的API文档请参考 [api.md](docs/api.md)

### 主要接口
- `POST /api/upload` - 文件上传
- `GET /api/analysis/{id}` - 获取分析结果
- `POST /api/batch` - 批量处理
- `GET /api/history` - 历史记录

## 许可证

MIT License

## 联系方式

如有问题请提交Issue或联系开发者。
