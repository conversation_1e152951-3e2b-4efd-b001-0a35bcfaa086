@echo off
echo ========================================
echo SDCC编译器配置脚本
echo ========================================

set SDCC_PATH=D:\sdcc

echo 检查SDCC目录...
if not exist "%SDCC_PATH%" (
    echo ✗ 错误: 未找到SDCC目录 %SDCC_PATH%
    echo 请确认SDCC已解压到正确位置
    pause
    exit /b 1
)

echo ✓ 找到SDCC目录: %SDCC_PATH%

echo.
echo 检查SDCC可执行文件...
if exist "%SDCC_PATH%\bin\sdcc.exe" (
    echo ✓ 找到sdcc.exe
) else (
    echo ✗ 未找到sdcc.exe，请检查SDCC目录结构
    echo 正确的目录结构应该是:
    echo   D:\sdcc\
    echo   ├── bin\
    echo   │   ├── sdcc.exe
    echo   │   ├── packihx.exe
    echo   │   └── ...
    echo   ├── include\
    echo   └── lib\
    pause
    exit /b 1
)

echo.
echo 设置临时环境变量...
set PATH=%SDCC_PATH%\bin;%PATH%

echo.
echo 测试SDCC编译器...
sdcc --version
if %ERRORLEVEL% EQU 0 (
    echo ✓ SDCC编译器工作正常
) else (
    echo ✗ SDCC编译器测试失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 配置完成！
echo ========================================
echo.
echo 当前会话中SDCC已可用，但重启后需要重新运行此脚本
echo 或者手动将以下路径添加到系统PATH环境变量:
echo %SDCC_PATH%\bin
echo.
echo 现在可以编译激光雕刻机项目了！
echo.

choice /C YN /M "是否立即编译激光雕刻机项目"
if %ERRORLEVEL% EQU 1 (
    echo.
    echo 开始编译...
    cd /d "%~dp0LaserEngraver_Control_System\MCU_Program_SDCC"
    call build.bat
) else (
    echo.
    echo 稍后可以运行以下命令编译:
    echo cd LaserEngraver_Control_System\MCU_Program_SDCC
    echo build.bat
)

echo.
pause
