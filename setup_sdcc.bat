@echo off
chcp 65001 >nul
echo ========================================
echo SDCC Compiler Setup Script
echo ========================================

set SDCC_PATH=D:\sdcc

echo Checking SDCC directory...
if not exist "%SDCC_PATH%" (
    echo [ERROR] SDCC directory not found: %SDCC_PATH%
    echo Please confirm SDCC is extracted to the correct location
    echo.
    echo Trying to find SDCC in common locations...
    if exist "D:\sdcc-4.2.0" set SDCC_PATH=D:\sdcc-4.2.0
    if exist "D:\sdcc-4.3.0" set SDCC_PATH=D:\sdcc-4.3.0
    if exist "D:\sdcc-4.4.0" set SDCC_PATH=D:\sdcc-4.4.0
    if exist "C:\sdcc" set SDCC_PATH=C:\sdcc

    if not exist "%SDCC_PATH%" (
        echo [ERROR] Cannot find SDCC installation
        pause
        exit /b 1
    )
)

echo [OK] Found SDCC directory: %SDCC_PATH%

echo.
echo Checking SDCC executable files...
if exist "%SDCC_PATH%\bin\sdcc.exe" (
    echo [OK] Found sdcc.exe
) else (
    echo [ERROR] sdcc.exe not found, checking directory structure
    echo Expected directory structure:
    echo   D:\sdcc\
    echo   ├── bin\
    echo   │   ├── sdcc.exe
    echo   │   ├── packihx.exe
    echo   │   └── ...
    echo   ├── include\
    echo   └── lib\
    echo.
    echo Current directory contents:
    dir "%SDCC_PATH%" /b
    pause
    exit /b 1
)

echo.
echo Setting temporary environment variables...
set PATH=%SDCC_PATH%\bin;%PATH%

echo.
echo Testing SDCC compiler...
sdcc --version
if %ERRORLEVEL% EQU 0 (
    echo [OK] SDCC compiler is working properly
) else (
    echo [ERROR] SDCC compiler test failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo SDCC is available in current session, but you need to run this script again after restart
echo Or manually add the following path to system PATH environment variable:
echo %SDCC_PATH%\bin
echo.
echo Now you can compile the laser engraver project!
echo.

choice /C YN /M "Compile laser engraver project now? (Y/N)"
if %ERRORLEVEL% EQU 1 (
    echo.
    echo Starting compilation...
    cd /d "%~dp0LaserEngraver_Control_System\MCU_Program_SDCC"
    call build.bat
) else (
    echo.
    echo You can compile later with these commands:
    echo cd LaserEngraver_Control_System\MCU_Program_SDCC
    echo build.bat
)

echo.
pause
