# 激光雕刻机控制系统测试指南

## 1. 测试环境准备

### 1.1 硬件要求
- STC15W201S单片机开发板
- USB转串口模块
- 步进电机驱动器 x2
- 步进电机 x2 (X轴、Y轴)
- 激光模块 (可选，测试时可用LED代替)
- 电源模块 (5V/12V)

### 1.2 软件要求
- Keil uVision 5 (单片机程序编译)
- Visual Studio 2019/2022 (上位机程序编译)
- 串口调试助手 (可选)

## 2. 单片机程序测试

### 2.1 编译和下载
1. 打开Keil工程文件: `MCU_Program/Keil_Project/LaserEngraver.uvproj`
2. 编译项目，确保无错误
3. 将生成的HEX文件下载到STC15W201S芯片

### 2.2 硬件连接
```
STC15W201S引脚连接:
- P3.0 (引脚9)  -> USB转串口RX
- P3.1 (引脚10) -> USB转串口TX
- P1.0-P1.3 (引脚15,16,1,2) -> X轴步进电机驱动器
- P3.2,P3.3,P3.6,P3.7 (引脚11,12,13,14) -> Y轴步进电机驱动器
- P2.0 -> 激光PWM控制 (可接LED测试)
- P2.1 -> 激光使能控制
```

### 2.3 基本功能测试
1. **串口通信测试**
   - 波特率: 9600
   - 发送: `<STX>Q<ETX>00` (查询位置)
   - 期望返回: `<STX>OKX:0,Y:0<ETX>00`

2. **运动控制测试**
   - 发送: `<STX>M100,100<ETX>00` (移动到100,100)
   - 观察步进电机是否转动

3. **激光控制测试**
   - 发送: `<STX>L50<ETX>00` (激光功率50%)
   - 观察激光模块或LED是否点亮

## 3. 上位机程序测试

### 3.1 编译和运行
1. 打开Visual Studio项目: `HostPC_Program/LaserEngraverControl.csproj`
2. 编译项目，确保无错误
3. 运行程序

### 3.2 界面功能测试
1. **串口连接测试**
   - 选择正确的串口和波特率
   - 点击"连接"按钮
   - 确认连接状态显示正常

2. **运动控制测试**
   - 在X、Y坐标框中输入数值
   - 点击"移动到"按钮
   - 观察单片机响应和电机运动

3. **激光控制测试**
   - 设置激光功率
   - 点击"开启"/"关闭"按钮
   - 观察激光状态变化

4. **文件加载测试**
   - 点击"浏览"按钮加载测试文件
   - 查看预览窗口是否显示路径
   - 确认"开始雕刻"按钮可用

## 4. 系统集成测试

### 4.1 通信协议测试
测试所有定义的命令:

| 命令 | 格式 | 功能 | 测试方法 |
|------|------|------|----------|
| M | M100,200 | 移动到指定位置 | 发送命令，观察运动 |
| R | R10,-5 | 相对移动 | 发送命令，观察运动 |
| V | V50 | 设置速度 | 发送命令，测试运动速度 |
| L | L80 | 激光开启 | 发送命令，观察激光状态 |
| O | O | 激光关闭 | 发送命令，观察激光状态 |
| Q | Q | 查询位置 | 发送命令，检查返回值 |
| T | T | 查询状态 | 发送命令，检查返回值 |

### 4.2 文件解析测试
创建测试G-code文件:
```gcode
G0 X0 Y0        ; 移动到原点
M3 S50          ; 激光开启，功率50%
G1 X100 Y0 F500 ; 直线到(100,0)
G1 X100 Y100    ; 直线到(100,100)
G1 X0 Y100      ; 直线到(0,100)
G1 X0 Y0        ; 直线到(0,0)
M5              ; 激光关闭
```

### 4.3 完整雕刻流程测试
1. 连接硬件和串口
2. 加载测试文件
3. 开始雕刻
4. 观察整个雕刻过程
5. 测试暂停/恢复功能
6. 测试停止功能

## 5. 性能测试

### 5.1 通信性能
- 测试命令响应时间
- 测试连续命令处理能力
- 测试错误恢复机制

### 5.2 运动精度
- 测试定位精度
- 测试重复定位精度
- 测试运动平滑性

### 5.3 激光控制精度
- 测试功率控制精度
- 测试开关响应时间
- 测试PWM频率稳定性

## 6. 故障排除

### 6.1 常见问题
1. **串口连接失败**
   - 检查串口号是否正确
   - 检查波特率设置
   - 检查硬件连接

2. **单片机无响应**
   - 检查程序是否正确下载
   - 检查电源供电
   - 检查晶振工作

3. **步进电机不转**
   - 检查驱动器连接
   - 检查电源电压
   - 检查相序连接

4. **激光不工作**
   - 检查激光模块连接
   - 检查PWM信号
   - 检查使能信号

### 6.2 调试方法
1. 使用串口调试助手监控通信
2. 使用示波器检查PWM信号
3. 使用万用表检查电压
4. 查看上位机日志信息

## 7. 测试报告模板

### 7.1 测试结果记录
- [ ] 单片机程序编译通过
- [ ] 上位机程序编译通过
- [ ] 串口通信正常
- [ ] 运动控制正常
- [ ] 激光控制正常
- [ ] 文件解析正常
- [ ] 完整雕刻流程正常

### 7.2 性能指标
- 命令响应时间: ___ms
- 定位精度: ___mm
- 最大运动速度: ___%
- 激光功率精度: ___%

### 7.3 问题记录
| 问题描述 | 严重程度 | 解决方案 | 状态 |
|----------|----------|----------|------|
|          |          |          |      |

## 8. 验收标准

系统通过测试的标准:
1. 所有基本功能正常工作
2. 通信协议完全兼容
3. 运动精度满足要求 (±0.1mm)
4. 激光控制精度满足要求 (±1%)
5. 无严重安全隐患
6. 用户界面友好易用

## 9. 后续优化建议

1. 增加限位开关检测
2. 添加温度保护功能
3. 优化路径规划算法
4. 增加更多文件格式支持
5. 添加网络控制功能
