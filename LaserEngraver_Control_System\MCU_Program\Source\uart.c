/*******************************************************************************
 * 串口通信模块实现
 * 功能: 实现与上位机的串口通信
 ******************************************************************************/

#include "uart.h"

/*******************************************************************************
 * 全局变量定义
 ******************************************************************************/
volatile uint8_t uart_rx_buffer[UART_BUFFER_SIZE];
volatile uint8_t uart_rx_head = 0;
volatile uint8_t uart_rx_tail = 0;
volatile uint8_t uart_tx_buffer[UART_BUFFER_SIZE];
volatile uint8_t uart_tx_head = 0;
volatile uint8_t uart_tx_tail = 0;
volatile uint8_t uart_tx_busy = 0;

/*******************************************************************************
 * 函数: UART_Init
 * 功能: 串口初始化
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void UART_Init(void)
{
    // 配置串口工作模式
    SCON = 0x50;    // 8位数据，可变波特率
    
    // 配置定时器1作为波特率发生器
    TMOD &= 0x0F;   // 清除定时器1模式位
    TMOD |= 0x20;   // 定时器1工作在模式2（8位自动重装载）
    
    // 计算波特率重装载值
    // 波特率 = FOSC / (32 * 12 * (256 - TH1))
    // TH1 = 256 - FOSC / (32 * 12 * 波特率)
    TH1 = 256 - (FOSC / 32 / 12 / BAUD);
    TL1 = TH1;
    
    // 启动定时器1
    TR1 = 1;
    
    // 使能串口中断
    ES = 1;
    
    // 清空缓冲区
    UART_ClearBuffer();
}

/*******************************************************************************
 * 函数: UART_SendByte
 * 功能: 发送单字节数据
 * 参数: byte - 要发送的字节
 * 返回: 无
 ******************************************************************************/
void UART_SendByte(uint8_t byte)
{
    // 等待发送缓冲区有空间
    while(((uart_tx_head + 1) % UART_BUFFER_SIZE) == uart_tx_tail);
    
    // 将数据放入发送缓冲区
    uart_tx_buffer[uart_tx_head] = byte;
    uart_tx_head = (uart_tx_head + 1) % UART_BUFFER_SIZE;
    
    // 如果发送器空闲，启动发送
    if(!uart_tx_busy)
    {
        uart_tx_busy = 1;
        SBUF = uart_tx_buffer[uart_tx_tail];
        uart_tx_tail = (uart_tx_tail + 1) % UART_BUFFER_SIZE;
    }
}

/*******************************************************************************
 * 函数: UART_SendString
 * 功能: 发送字符串
 * 参数: str - 要发送的字符串
 * 返回: 无
 ******************************************************************************/
void UART_SendString(char* str)
{
    while(*str)
    {
        UART_SendByte(*str++);
    }
}

/*******************************************************************************
 * 函数: UART_SendData
 * 功能: 发送数据数组
 * 参数: data - 数据指针, len - 数据长度
 * 返回: 无
 ******************************************************************************/
void UART_SendData(uint8_t* data, uint8_t len)
{
    uint8_t i;
    for(i = 0; i < len; i++)
    {
        UART_SendByte(data[i]);
    }
}

/*******************************************************************************
 * 函数: UART_ReceiveByte
 * 功能: 接收单字节数据
 * 参数: 无
 * 返回: 接收到的字节
 ******************************************************************************/
uint8_t UART_ReceiveByte(void)
{
    uint8_t byte;
    
    // 等待接收缓冲区有数据
    while(uart_rx_head == uart_rx_tail);
    
    // 从接收缓冲区取出数据
    byte = uart_rx_buffer[uart_rx_tail];
    uart_rx_tail = (uart_rx_tail + 1) % UART_BUFFER_SIZE;
    
    return byte;
}

/*******************************************************************************
 * 函数: UART_DataAvailable
 * 功能: 检查是否有数据可读
 * 参数: 无
 * 返回: 可读数据的字节数
 ******************************************************************************/
uint8_t UART_DataAvailable(void)
{
    return (uart_rx_head - uart_rx_tail + UART_BUFFER_SIZE) % UART_BUFFER_SIZE;
}

/*******************************************************************************
 * 函数: UART_ReceiveISR
 * 功能: 串口接收中断服务程序
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void UART_ReceiveISR(void)
{
    uint8_t next_head;
    
    // 计算下一个头指针位置
    next_head = (uart_rx_head + 1) % UART_BUFFER_SIZE;
    
    // 如果缓冲区未满，存储接收到的数据
    if(next_head != uart_rx_tail)
    {
        uart_rx_buffer[uart_rx_head] = SBUF;
        uart_rx_head = next_head;
    }
    // 缓冲区满时丢弃数据（可以添加错误处理）
}

/*******************************************************************************
 * 函数: UART_TransmitISR
 * 功能: 串口发送中断服务程序
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void UART_TransmitISR(void)
{
    // 如果发送缓冲区还有数据
    if(uart_tx_head != uart_tx_tail)
    {
        SBUF = uart_tx_buffer[uart_tx_tail];
        uart_tx_tail = (uart_tx_tail + 1) % UART_BUFFER_SIZE;
    }
    else
    {
        uart_tx_busy = 0;  // 发送完成
    }
}

/*******************************************************************************
 * 函数: UART_ClearBuffer
 * 功能: 清空接收缓冲区
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void UART_ClearBuffer(void)
{
    uart_rx_head = 0;
    uart_rx_tail = 0;
    uart_tx_head = 0;
    uart_tx_tail = 0;
    uart_tx_busy = 0;
}
