# ============================================================================
# Makefile for LaserEngraver Control System
# 支持 SDCC 和 Keil C51 编译器
# ============================================================================

# 项目配置
PROJECT_NAME = LaserEngraver
SOURCE_DIR = Source
BUILD_DIR = Build
INCLUDE_DIR = Source

# 源文件列表
SOURCES = main.c uart.c stepper.c laser.c protocol.c system.c
OBJECTS = $(SOURCES:.c=.rel)
OBJECTS_PATH = $(addprefix $(BUILD_DIR)/, $(OBJECTS))

# 编译器选择 (sdcc 或 keil)
COMPILER ?= sdcc

# SDCC 编译器配置
ifeq ($(COMPILER), sdcc)
    CC = sdcc
    CFLAGS = --model-large --xram-size 1024 --code-size 8192 -O2 --std-c99
    LDFLAGS = --model-large
    OUTPUT_EXT = ihx
    OBJECT_EXT = rel
endif

# Keil C51 编译器配置 (需要安装Keil)
ifeq ($(COMPILER), keil)
    CC = c51
    CFLAGS = LARGE OPTIMIZE(9,SPEED)
    LDFLAGS = 
    OUTPUT_EXT = hex
    OBJECT_EXT = obj
endif

# 默认目标
all: $(BUILD_DIR)/$(PROJECT_NAME).$(OUTPUT_EXT)

# 创建构建目录
$(BUILD_DIR):
	@echo "创建构建目录..."
	@mkdir -p $(BUILD_DIR)

# 编译规则
$(BUILD_DIR)/%.$(OBJECT_EXT): $(SOURCE_DIR)/%.c | $(BUILD_DIR)
	@echo "编译 $<..."
ifeq ($(COMPILER), sdcc)
	$(CC) $(CFLAGS) -I$(INCLUDE_DIR) -c $< -o $@
else
	$(CC) $< $(CFLAGS) OBJECT($@)
endif

# 链接规则
$(BUILD_DIR)/$(PROJECT_NAME).$(OUTPUT_EXT): $(OBJECTS_PATH)
	@echo "链接生成 $@..."
ifeq ($(COMPILER), sdcc)
	$(CC) $(LDFLAGS) $(OBJECTS_PATH) -o $@
else
	bl51 $(OBJECTS_PATH) TO $@
endif
	@echo "编译完成！"

# 清理
clean:
	@echo "清理构建文件..."
	@rm -rf $(BUILD_DIR)
	@echo "清理完成。"

# 显示帮助
help:
	@echo "LaserEngraver 控制系统编译帮助"
	@echo "================================"
	@echo "可用目标:"
	@echo "  all     - 编译整个项目 (默认)"
	@echo "  clean   - 清理构建文件"
	@echo "  help    - 显示此帮助信息"
	@echo ""
	@echo "编译器选择:"
	@echo "  make COMPILER=sdcc  - 使用SDCC编译器 (默认)"
	@echo "  make COMPILER=keil  - 使用Keil C51编译器"
	@echo ""
	@echo "示例:"
	@echo "  make                - 使用SDCC编译"
	@echo "  make clean          - 清理文件"
	@echo "  make COMPILER=keil  - 使用Keil编译"

# 安装SDCC (Windows)
install-sdcc:
	@echo "请从以下网址下载并安装SDCC:"
	@echo "http://sdcc.sourceforge.net/"
	@echo "或使用包管理器:"
	@echo "  choco install sdcc"
	@echo "  scoop install sdcc"

# 显示项目信息
info:
	@echo "项目信息"
	@echo "========"
	@echo "项目名称: $(PROJECT_NAME)"
	@echo "源文件目录: $(SOURCE_DIR)"
	@echo "构建目录: $(BUILD_DIR)"
	@echo "当前编译器: $(COMPILER)"
	@echo "源文件列表:"
	@for file in $(SOURCES); do echo "  - $$file"; done

# 检查语法 (仅SDCC)
syntax-check:
ifeq ($(COMPILER), sdcc)
	@echo "检查语法..."
	@for file in $(SOURCES); do \
		echo "检查 $(SOURCE_DIR)/$$file..."; \
		$(CC) $(CFLAGS) -I$(INCLUDE_DIR) -fsyntax-only $(SOURCE_DIR)/$$file; \
	done
	@echo "语法检查完成。"
else
	@echo "语法检查仅支持SDCC编译器。"
endif

# 伪目标
.PHONY: all clean help install-sdcc info syntax-check
