/*******************************************************************************
 * 激光控制模块头文件
 * 功能: 控制激光器的开关和功率
 ******************************************************************************/

#ifndef __LASER_H__
#define __LASER_H__

#include "STC15W201S.h"

/*******************************************************************************
 * 宏定义
 ******************************************************************************/
#define LASER_OFF           0       // 激光关闭
#define LASER_ON            1       // 激光开启

#define LASER_MIN_POWER     0       // 最小功率
#define LASER_MAX_POWER     100     // 最大功率

/*******************************************************************************
 * 函数声明
 ******************************************************************************/
void Laser_Init(void);                      // 激光器初始化
void Laser_SetPower(uint8_t power);         // 设置激光功率
void Laser_On(void);                        // 开启激光
void Laser_Off(void);                       // 关闭激光
uint8_t Laser_GetPower(void);               // 获取当前功率
uint8_t Laser_GetState(void);               // 获取激光状态
void Laser_Process(void);                   // 激光处理函数
void Laser_TimerISR(void);                  // 定时器中断服务程序

/*******************************************************************************
 * 全局变量声明
 ******************************************************************************/
extern volatile uint8_t laser_power;
extern volatile uint8_t laser_state;
extern volatile uint8_t laser_pwm_counter;

#endif // __LASER_H__
