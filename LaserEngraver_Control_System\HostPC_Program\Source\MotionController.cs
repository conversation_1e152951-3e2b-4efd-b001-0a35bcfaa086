/*******************************************************************************
 * 运动控制器类
 * 功能: 控制X轴和Y轴的运动
 ******************************************************************************/

using System;
using System.Drawing;

namespace LaserEngraverControl
{
    /// <summary>
    /// 运动控制器类
    /// </summary>
    public class MotionController
    {
        #region 私有字段
        private SerialCommunication serialComm;
        private Point currentPosition = new Point(0, 0);
        private int currentSpeed = 50;
        private bool isMoving = false;
        #endregion

        #region 事件定义
        public event EventHandler<string> StatusChanged;
        public event EventHandler<Point> PositionChanged;
        public event EventHandler<int> SpeedChanged;
        public event EventHandler<bool> MovingStateChanged;
        #endregion

        #region 属性
        /// <summary>
        /// 当前位置
        /// </summary>
        public Point CurrentPosition
        {
            get { return currentPosition; }
        }

        /// <summary>
        /// 当前速度 (1-100)
        /// </summary>
        public int CurrentSpeed
        {
            get { return currentSpeed; }
        }

        /// <summary>
        /// 是否正在运动
        /// </summary>
        public bool IsMoving
        {
            get { return isMoving; }
        }
        #endregion

        #region 构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="serialCommunication">串口通信对象</param>
        public MotionController(SerialCommunication serialCommunication)
        {
            serialComm = serialCommunication ?? throw new ArgumentNullException(nameof(serialCommunication));
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <returns>移动命令是否发送成功</returns>
        public bool MoveTo(int x, int y)
        {
            try
            {
                string data = $"{x},{y}";
                bool success = serialComm.SendCommand('M', data);
                
                if (success)
                {
                    isMoving = true;
                    MovingStateChanged?.Invoke(this, true);
                    StatusChanged?.Invoke(this, $"移动到位置 ({x}, {y})");
                    
                    // 更新当前位置 (实际应该等待单片机确认)
                    currentPosition = new Point(x, y);
                    PositionChanged?.Invoke(this, currentPosition);
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"移动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 移动到指定位置
        /// </summary>
        /// <param name="position">目标位置</param>
        /// <returns>移动命令是否发送成功</returns>
        public bool MoveTo(Point position)
        {
            return MoveTo(position.X, position.Y);
        }

        /// <summary>
        /// 相对移动
        /// </summary>
        /// <param name="deltaX">X轴偏移</param>
        /// <param name="deltaY">Y轴偏移</param>
        /// <returns>移动命令是否发送成功</returns>
        public bool MoveRelative(int deltaX, int deltaY)
        {
            try
            {
                string data = $"{deltaX},{deltaY}";
                bool success = serialComm.SendCommand('R', data);
                
                if (success)
                {
                    isMoving = true;
                    MovingStateChanged?.Invoke(this, true);
                    StatusChanged?.Invoke(this, $"相对移动 ({deltaX}, {deltaY})");
                    
                    // 更新当前位置
                    currentPosition.X += deltaX;
                    currentPosition.Y += deltaY;
                    PositionChanged?.Invoke(this, currentPosition);
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"相对移动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置运动速度
        /// </summary>
        /// <param name="speed">速度百分比 (1-100)</param>
        /// <returns>设置是否成功</returns>
        public bool SetSpeed(int speed)
        {
            // 限制速度范围
            if (speed < 1) speed = 1;
            if (speed > 100) speed = 100;

            try
            {
                bool success = serialComm.SendCommand('V', speed.ToString());
                
                if (success)
                {
                    currentSpeed = speed;
                    SpeedChanged?.Invoke(this, speed);
                    StatusChanged?.Invoke(this, $"速度设置为 {speed}%");
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"设置速度失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 回零操作
        /// </summary>
        /// <returns>回零命令是否发送成功</returns>
        public bool Home()
        {
            return MoveTo(0, 0);
        }

        /// <summary>
        /// 停止运动
        /// </summary>
        /// <returns>停止命令是否发送成功</returns>
        public bool Stop()
        {
            try
            {
                bool success = serialComm.SendCommand('S');
                
                if (success)
                {
                    isMoving = false;
                    MovingStateChanged?.Invoke(this, false);
                    StatusChanged?.Invoke(this, "运动已停止");
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"停止运动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 暂停运动
        /// </summary>
        /// <returns>暂停命令是否发送成功</returns>
        public bool Pause()
        {
            try
            {
                bool success = serialComm.SendCommand('P');
                
                if (success)
                {
                    StatusChanged?.Invoke(this, "运动已暂停");
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"暂停运动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 恢复运动
        /// </summary>
        /// <returns>恢复命令是否发送成功</returns>
        public bool Resume()
        {
            try
            {
                bool success = serialComm.SendCommand('C');
                
                if (success)
                {
                    StatusChanged?.Invoke(this, "运动已恢复");
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"恢复运动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 查询当前位置
        /// </summary>
        /// <returns>查询命令是否发送成功</returns>
        public bool QueryPosition()
        {
            try
            {
                bool success = serialComm.SendCommand('Q');
                
                if (success)
                {
                    StatusChanged?.Invoke(this, "查询位置信息");
                }

                return success;
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"查询位置失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 设置当前位置 (用于校准)
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        public void SetCurrentPosition(int x, int y)
        {
            currentPosition = new Point(x, y);
            PositionChanged?.Invoke(this, currentPosition);
            StatusChanged?.Invoke(this, $"当前位置设置为 ({x}, {y})");
        }

        /// <summary>
        /// 设置当前位置 (用于校准)
        /// </summary>
        /// <param name="position">位置</param>
        public void SetCurrentPosition(Point position)
        {
            SetCurrentPosition(position.X, position.Y);
        }

        /// <summary>
        /// 获取运动状态信息
        /// </summary>
        /// <returns>状态信息字符串</returns>
        public string GetStatusInfo()
        {
            return $"位置: ({currentPosition.X}, {currentPosition.Y}), 速度: {currentSpeed}%, 状态: {(isMoving ? "运动中" : "停止")}";
        }

        /// <summary>
        /// 计算两点之间的距离
        /// </summary>
        /// <param name="point1">点1</param>
        /// <param name="point2">点2</param>
        /// <returns>距离</returns>
        public static double CalculateDistance(Point point1, Point point2)
        {
            int deltaX = point2.X - point1.X;
            int deltaY = point2.Y - point1.Y;
            return Math.Sqrt(deltaX * deltaX + deltaY * deltaY);
        }
        #endregion
    }
}
