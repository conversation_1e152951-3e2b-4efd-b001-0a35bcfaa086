/*******************************************************************************
 * 主窗体代码文件
 * 功能: 激光雕刻机控制系统主界面
 ******************************************************************************/

using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace LaserEngraverControl
{
    /// <summary>
    /// 主窗体类
    /// </summary>
    public partial class MainForm : Form
    {
        #region 私有字段
        private SerialCommunication serialComm;
        private LaserController laserController;
        private MotionController motionController;
        private FileParser fileParser;
        private PathPlanner pathPlanner;
        private List<PathPoint> currentPath;
        private bool isEngraving = false;
        private bool isPaused = false;
        private int currentPathIndex = 0;
        #endregion

        #region 构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        public MainForm()
        {
            InitializeComponent();
            InitializeComponents();
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            // 初始化串口通信
            serialComm = new SerialCommunication();
            serialComm.DataReceived += SerialComm_DataReceived;
            serialComm.ErrorOccurred += SerialComm_ErrorOccurred;
            serialComm.ConnectionChanged += SerialComm_ConnectionChanged;

            // 初始化控制器
            laserController = new LaserController(serialComm);
            laserController.StatusChanged += LaserController_StatusChanged;
            laserController.PowerChanged += LaserController_PowerChanged;

            motionController = new MotionController(serialComm);
            motionController.StatusChanged += MotionController_StatusChanged;
            motionController.PositionChanged += MotionController_PositionChanged;
            motionController.SpeedChanged += MotionController_SpeedChanged;

            // 初始化解析器和规划器
            fileParser = new FileParser();
            fileParser.StatusChanged += FileParser_StatusChanged;
            fileParser.ProgressChanged += FileParser_ProgressChanged;

            pathPlanner = new PathPlanner();
            pathPlanner.StatusChanged += PathPlanner_StatusChanged;
            pathPlanner.ProgressChanged += PathPlanner_ProgressChanged;

            // 初始化路径列表
            currentPath = new List<PathPoint>();
        }
        #endregion

        #region 窗体事件
        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void MainForm_Load(object sender, EventArgs e)
        {
            // 初始化串口列表
            RefreshPortList();
            
            // 设置默认波特率
            cmbBaudRate.SelectedIndex = 0; // 9600
            
            // 初始化状态
            UpdateStatus("系统已启动，请连接串口");
            
            // 初始化预览图片
            InitializePreview();
            
            LogMessage("激光雕刻机控制系统已启动");
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 停止雕刻
            if (isEngraving)
            {
                StopEngraving();
            }

            // 关闭激光
            laserController?.TurnOff();

            // 断开串口连接
            serialComm?.Disconnect();
            serialComm?.Dispose();

            LogMessage("系统已关闭");
        }
        #endregion

        #region 串口相关事件
        /// <summary>
        /// 串口数据接收事件
        /// </summary>
        private void SerialComm_DataReceived(object sender, string data)
        {
            this.Invoke(new Action(() =>
            {
                LogMessage($"接收: {data}");
                // 这里可以解析接收到的数据
                ParseReceivedData(data);
            }));
        }

        /// <summary>
        /// 串口错误事件
        /// </summary>
        private void SerialComm_ErrorOccurred(object sender, string error)
        {
            this.Invoke(new Action(() =>
            {
                LogMessage($"串口错误: {error}");
                UpdateStatus($"串口错误: {error}");
            }));
        }

        /// <summary>
        /// 串口连接状态变化事件
        /// </summary>
        private void SerialComm_ConnectionChanged(object sender, EventArgs e)
        {
            this.Invoke(new Action(() =>
            {
                bool connected = serialComm.IsConnected;
                btnConnect.Enabled = !connected;
                btnDisconnect.Enabled = connected;
                cmbPortName.Enabled = !connected;
                cmbBaudRate.Enabled = !connected;
                
                UpdateStatus(connected ? $"已连接到 {serialComm.PortName}" : "串口已断开");
                LogMessage(connected ? $"串口连接成功: {serialComm.PortName}" : "串口连接已断开");
            }));
        }
        #endregion

        #region 控制器事件
        /// <summary>
        /// 激光控制器状态变化事件
        /// </summary>
        private void LaserController_StatusChanged(object sender, string status)
        {
            this.Invoke(new Action(() =>
            {
                LogMessage($"激光: {status}");
            }));
        }

        /// <summary>
        /// 激光功率变化事件
        /// </summary>
        private void LaserController_PowerChanged(object sender, int power)
        {
            this.Invoke(new Action(() =>
            {
                numPower.Value = power;
            }));
        }

        /// <summary>
        /// 运动控制器状态变化事件
        /// </summary>
        private void MotionController_StatusChanged(object sender, string status)
        {
            this.Invoke(new Action(() =>
            {
                LogMessage($"运动: {status}");
            }));
        }

        /// <summary>
        /// 位置变化事件
        /// </summary>
        private void MotionController_PositionChanged(object sender, Point position)
        {
            this.Invoke(new Action(() =>
            {
                numX.Value = position.X;
                numY.Value = position.Y;
            }));
        }

        /// <summary>
        /// 速度变化事件
        /// </summary>
        private void MotionController_SpeedChanged(object sender, int speed)
        {
            this.Invoke(new Action(() =>
            {
                numSpeed.Value = speed;
            }));
        }
        #endregion

        #region 文件解析器事件
        /// <summary>
        /// 文件解析器状态变化事件
        /// </summary>
        private void FileParser_StatusChanged(object sender, string status)
        {
            this.Invoke(new Action(() =>
            {
                UpdateStatus(status);
                LogMessage($"解析: {status}");
            }));
        }

        /// <summary>
        /// 文件解析进度变化事件
        /// </summary>
        private void FileParser_ProgressChanged(object sender, int progress)
        {
            this.Invoke(new Action(() =>
            {
                progressBar.Value = progress;
            }));
        }
        #endregion

        #region 路径规划器事件
        /// <summary>
        /// 路径规划器状态变化事件
        /// </summary>
        private void PathPlanner_StatusChanged(object sender, string status)
        {
            this.Invoke(new Action(() =>
            {
                UpdateStatus(status);
                LogMessage($"规划: {status}");
            }));
        }

        /// <summary>
        /// 路径规划进度变化事件
        /// </summary>
        private void PathPlanner_ProgressChanged(object sender, int progress)
        {
            this.Invoke(new Action(() =>
            {
                progressBar.Value = progress;
            }));
        }
        #endregion

        #region 按钮事件处理
        /// <summary>
        /// 连接按钮点击事件
        /// </summary>
        private void btnConnect_Click(object sender, EventArgs e)
        {
            if (cmbPortName.SelectedItem == null)
            {
                MessageBox.Show("请选择串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string portName = cmbPortName.SelectedItem.ToString();
            int baudRate = int.Parse(cmbBaudRate.SelectedItem.ToString());

            if (serialComm.Connect(portName, baudRate))
            {
                LogMessage($"正在连接到 {portName}...");
            }
        }

        /// <summary>
        /// 断开连接按钮点击事件
        /// </summary>
        private void btnDisconnect_Click(object sender, EventArgs e)
        {
            serialComm.Disconnect();
        }

        /// <summary>
        /// 移动到指定位置按钮点击事件
        /// </summary>
        private void btnMoveTo_Click(object sender, EventArgs e)
        {
            if (!serialComm.IsConnected)
            {
                MessageBox.Show("请先连接串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int x = (int)numX.Value;
            int y = (int)numY.Value;
            motionController.MoveTo(x, y);
        }

        /// <summary>
        /// 设置速度按钮点击事件
        /// </summary>
        private void btnSetSpeed_Click(object sender, EventArgs e)
        {
            if (!serialComm.IsConnected)
            {
                MessageBox.Show("请先连接串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int speed = (int)numSpeed.Value;
            motionController.SetSpeed(speed);
        }

        /// <summary>
        /// 停止按钮点击事件
        /// </summary>
        private void btnStop_Click(object sender, EventArgs e)
        {
            if (!serialComm.IsConnected)
            {
                MessageBox.Show("请先连接串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            motionController.Stop();
            
            if (isEngraving)
            {
                StopEngraving();
            }
        }

        /// <summary>
        /// 回零按钮点击事件
        /// </summary>
        private void btnHome_Click(object sender, EventArgs e)
        {
            if (!serialComm.IsConnected)
            {
                MessageBox.Show("请先连接串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            motionController.Home();
        }

        /// <summary>
        /// 设置激光功率按钮点击事件
        /// </summary>
        private void btnSetPower_Click(object sender, EventArgs e)
        {
            if (!serialComm.IsConnected)
            {
                MessageBox.Show("请先连接串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int power = (int)numPower.Value;
            laserController.SetPower(power);
        }

        /// <summary>
        /// 激光开启按钮点击事件
        /// </summary>
        private void btnLaserOn_Click(object sender, EventArgs e)
        {
            if (!serialComm.IsConnected)
            {
                MessageBox.Show("请先连接串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int power = (int)numPower.Value;
            laserController.TurnOn(power);
        }

        /// <summary>
        /// 激光关闭按钮点击事件
        /// </summary>
        private void btnLaserOff_Click(object sender, EventArgs e)
        {
            if (!serialComm.IsConnected)
            {
                MessageBox.Show("请先连接串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            laserController.TurnOff();
        }

        /// <summary>
        /// 加载文件按钮点击事件
        /// </summary>
        private void btnLoadFile_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog dialog = new OpenFileDialog())
            {
                dialog.Filter = FileParser.GetFileFilter();
                dialog.Title = "选择要雕刻的文件";

                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    LoadFile(dialog.FileName);
                }
            }
        }

        /// <summary>
        /// 开始雕刻按钮点击事件
        /// </summary>
        private void btnStartEngraving_Click(object sender, EventArgs e)
        {
            if (!serialComm.IsConnected)
            {
                MessageBox.Show("请先连接串口", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (currentPath == null || currentPath.Count == 0)
            {
                MessageBox.Show("请先加载文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            StartEngraving();
        }

        /// <summary>
        /// 暂停/恢复按钮点击事件
        /// </summary>
        private void btnPauseResume_Click(object sender, EventArgs e)
        {
            if (isEngraving)
            {
                if (isPaused)
                {
                    ResumeEngraving();
                }
                else
                {
                    PauseEngraving();
                }
            }
        }
        #endregion

        #region 菜单事件处理
        /// <summary>
        /// 打开文件菜单点击事件
        /// </summary>
        private void openFileToolStripMenuItem_Click(object sender, EventArgs e)
        {
            btnLoadFile_Click(sender, e);
        }

        /// <summary>
        /// 退出菜单点击事件
        /// </summary>
        private void exitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 关于菜单点击事件
        /// </summary>
        private void aboutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            MessageBox.Show("激光雕刻机控制系统 v1.0.0\n\n开发者: AI Assistant\n日期: 2025-07-20",
                          "关于", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 刷新串口列表
        /// </summary>
        private void RefreshPortList()
        {
            cmbPortName.Items.Clear();
            string[] ports = SerialCommunication.GetAvailablePorts();
            cmbPortName.Items.AddRange(ports);

            if (ports.Length > 0)
            {
                cmbPortName.SelectedIndex = 0;
            }
        }

        /// <summary>
        /// 更新状态栏
        /// </summary>
        /// <param name="message">状态消息</param>
        private void UpdateStatus(string message)
        {
            statusLabel.Text = message;
        }

        /// <summary>
        /// 记录日志
        /// </summary>
        /// <param name="message">日志消息</param>
        private void LogMessage(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            string logEntry = $"[{timestamp}] {message}\r\n";

            txtLog.AppendText(logEntry);
            txtLog.ScrollToCaret();
        }

        /// <summary>
        /// 初始化预览
        /// </summary>
        private void InitializePreview()
        {
            pictureBox1.Paint += PictureBox1_Paint;
        }

        /// <summary>
        /// 预览绘制事件
        /// </summary>
        private void PictureBox1_Paint(object sender, PaintEventArgs e)
        {
            if (currentPath != null && currentPath.Count > 0)
            {
                DrawPath(e.Graphics);
            }
        }

        /// <summary>
        /// 绘制路径
        /// </summary>
        /// <param name="graphics">绘图对象</param>
        private void DrawPath(Graphics graphics)
        {
            if (currentPath == null || currentPath.Count == 0)
                return;

            graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;

            Pen movePen = new Pen(Color.Blue, 1) { DashStyle = System.Drawing.Drawing2D.DashStyle.Dash };
            Pen cutPen = new Pen(Color.Red, 2);

            PointF lastPoint = PointF.Empty;

            foreach (PathPoint point in currentPath)
            {
                PointF currentPoint = new PointF(point.Position.X, point.Position.Y);

                if (!lastPoint.IsEmpty)
                {
                    Pen pen = (point.Type == PathPointType.MoveTo) ? movePen : cutPen;
                    graphics.DrawLine(pen, lastPoint, currentPoint);
                }

                lastPoint = currentPoint;
            }

            movePen.Dispose();
            cutPen.Dispose();
        }

        /// <summary>
        /// 解析接收到的数据
        /// </summary>
        /// <param name="data">接收到的数据</param>
        private void ParseReceivedData(string data)
        {
            // 这里可以解析单片机返回的数据
            // 例如位置信息、状态信息等
            if (data.Contains("OK"))
            {
                // 命令执行成功
            }
            else if (data.Contains("ERR"))
            {
                // 命令执行失败
                LogMessage($"命令执行失败: {data}");
            }
        }

        /// <summary>
        /// 加载文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        private async void LoadFile(string filePath)
        {
            try
            {
                txtFilePath.Text = filePath;
                UpdateStatus("正在解析文件...");

                // 异步解析文件
                await Task.Run(() =>
                {
                    currentPath = fileParser.ParseFile(filePath);

                    // 优化路径
                    if (currentPath.Count > 0)
                    {
                        currentPath = pathPlanner.OptimizePath(currentPath);
                    }
                });

                // 更新预览
                pictureBox1.Invalidate();

                // 启用开始雕刻按钮
                btnStartEngraving.Enabled = currentPath.Count > 0;

                UpdateStatus($"文件加载完成，共{currentPath.Count}个路径点");
                LogMessage($"文件加载完成: {Path.GetFileName(filePath)}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文件失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatus("文件加载失败");
                LogMessage($"文件加载失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 开始雕刻
        /// </summary>
        private void StartEngraving()
        {
            isEngraving = true;
            isPaused = false;
            currentPathIndex = 0;

            btnStartEngraving.Enabled = false;
            btnPauseResume.Enabled = true;
            btnPauseResume.Text = "暂停";

            UpdateStatus("开始雕刻...");
            LogMessage("开始雕刻");

            // 开始执行路径
            ExecuteNextPathPoint();
        }

        /// <summary>
        /// 暂停雕刻
        /// </summary>
        private void PauseEngraving()
        {
            isPaused = true;
            btnPauseResume.Text = "恢复";

            motionController.Pause();
            laserController.TurnOff();

            UpdateStatus("雕刻已暂停");
            LogMessage("雕刻已暂停");
        }

        /// <summary>
        /// 恢复雕刻
        /// </summary>
        private void ResumeEngraving()
        {
            isPaused = false;
            btnPauseResume.Text = "暂停";

            motionController.Resume();

            UpdateStatus("雕刻已恢复");
            LogMessage("雕刻已恢复");

            // 继续执行路径
            ExecuteNextPathPoint();
        }

        /// <summary>
        /// 停止雕刻
        /// </summary>
        private void StopEngraving()
        {
            isEngraving = false;
            isPaused = false;

            btnStartEngraving.Enabled = true;
            btnPauseResume.Enabled = false;
            btnPauseResume.Text = "暂停";

            motionController.Stop();
            laserController.TurnOff();

            UpdateStatus("雕刻已停止");
            LogMessage("雕刻已停止");
        }

        /// <summary>
        /// 执行下一个路径点
        /// </summary>
        private void ExecuteNextPathPoint()
        {
            if (!isEngraving || isPaused || currentPathIndex >= currentPath.Count)
            {
                if (currentPathIndex >= currentPath.Count)
                {
                    // 雕刻完成
                    StopEngraving();
                    UpdateStatus("雕刻完成");
                    LogMessage("雕刻完成");
                }
                return;
            }

            PathPoint point = currentPath[currentPathIndex];

            // 执行路径点
            switch (point.Type)
            {
                case PathPointType.MoveTo:
                    laserController.TurnOff();
                    motionController.MoveTo((int)point.Position.X, (int)point.Position.Y);
                    break;

                case PathPointType.LineTo:
                    laserController.SetPower(point.LaserPower);
                    motionController.MoveTo((int)point.Position.X, (int)point.Position.Y);
                    break;
            }

            currentPathIndex++;

            // 更新进度
            int progress = (currentPathIndex * 100) / currentPath.Count;
            progressBar.Value = progress;

            // 这里应该等待运动完成后再执行下一个点
            // 简化处理，使用定时器模拟
            Timer timer = new Timer();
            timer.Interval = 100; // 100ms后执行下一个点
            timer.Tick += (s, e) =>
            {
                timer.Stop();
                timer.Dispose();
                ExecuteNextPathPoint();
            };
            timer.Start();
        }
        #endregion
    }
}
