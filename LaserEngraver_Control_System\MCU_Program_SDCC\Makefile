# Makefile for STC15W201S using SDCC
# 激光雕刻机控制系统 - SDCC版本

# 编译器设置
CC = sdcc
CFLAGS = -mmcs51 --model-small --std-c99
LDFLAGS = --code-size 0x3400 --xram-size 0x500 --iram-size 0x500

# 项目设置
PROJECT = LaserEngraver
TARGET = $(PROJECT).hex
SOURCES = main.c uart.c stepper.c laser.c protocol.c system.c

# 目标文件
OBJECTS = $(SOURCES:.c=.rel)

# 默认目标
all: $(TARGET)

# 编译规则
%.rel: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# 链接规则
$(PROJECT).ihx: $(OBJECTS)
	$(CC) $(CFLAGS) $(LDFLAGS) $(OBJECTS) -o $@

# 生成HEX文件
$(TARGET): $(PROJECT).ihx
	packihx $(PROJECT).ihx > $(TARGET)

# 清理
clean:
	rm -f *.rel *.ihx *.hex *.lst *.map *.mem *.lk *.rst *.sym *.asm

# 编译信息
info:
	@echo "项目: $(PROJECT)"
	@echo "目标: $(TARGET)"
	@echo "源文件: $(SOURCES)"
	@echo ""
	@echo "编译命令: make"
	@echo "清理命令: make clean"
	@echo "查看信息: make info"

.PHONY: all clean info
