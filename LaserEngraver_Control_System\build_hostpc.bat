@echo off
echo ========================================
echo 激光雕刻机控制系统 - 上位机程序编译脚本
echo ========================================

REM 设置MSBuild路径 (根据实际安装路径调整)
set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
if not exist %MSBUILD_PATH% (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
)
if not exist %MSBUILD_PATH% (
    echo 错误: 找不到MSBuild.exe，请检查Visual Studio安装路径
    pause
    exit /b 1
)

REM 设置项目路径
set PROJECT_PATH=HostPC_Program\LaserEngraverControl.csproj
set OUTPUT_DIR=HostPC_Program\bin\Release

echo 正在编译上位机程序...
echo 项目路径: %PROJECT_PATH%
echo 输出目录: %OUTPUT_DIR%
echo.

REM 清理之前的编译结果
if exist "%OUTPUT_DIR%" (
    echo 清理之前的编译结果...
    rmdir /s /q "%OUTPUT_DIR%"
)

REM 编译项目
echo 开始编译...
%MSBUILD_PATH% "%PROJECT_PATH%" /p:Configuration=Release /p:Platform="Any CPU" /p:OutputPath="%OUTPUT_DIR%"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo 编译成功！
    echo ========================================
    echo 可执行文件位置: %OUTPUT_DIR%\LaserEngraverControl.exe
    echo.
    
    REM 复制必要的文件
    echo 复制测试文件...
    if not exist "%OUTPUT_DIR%\TestFiles" mkdir "%OUTPUT_DIR%\TestFiles"
    copy "Documentation\test_files\*.gcode" "%OUTPUT_DIR%\TestFiles\" >nul 2>&1
    
    echo 复制文档...
    if not exist "%OUTPUT_DIR%\Documentation" mkdir "%OUTPUT_DIR%\Documentation"
    copy "Documentation\*.md" "%OUTPUT_DIR%\Documentation\" >nul 2>&1
    
    echo.
    echo 是否立即运行程序? (Y/N)
    set /p choice=
    if /i "%choice%"=="Y" (
        start "" "%OUTPUT_DIR%\LaserEngraverControl.exe"
    )
) else (
    echo.
    echo ========================================
    echo 编译失败！
    echo ========================================
    echo 请检查错误信息并修复后重试。
)

echo.
pause
