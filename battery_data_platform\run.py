#!/usr/bin/env python3
"""
电池数据云处理平台启动文件
"""

import os
from app import create_app, db
from app.models import User, UploadFile, ProcessingRecord

# 获取配置环境
config_name = os.environ.get('FLASK_ENV') or 'development'

# 创建应用实例
app = create_app(config_name)

@app.shell_context_processor
def make_shell_context():
    """为Flask shell提供上下文"""
    return {
        'db': db,
        'User': User,
        'UploadFile': UploadFile,
        'ProcessingRecord': ProcessingRecord
    }

@app.cli.command()
def init_db():
    """初始化数据库"""
    db.create_all()
    print("数据库初始化完成！")

@app.cli.command()
def create_admin():
    """创建管理员用户"""
    from app.models import User
    from werkzeug.security import generate_password_hash
    
    admin = User(
        username='admin',
        email='<EMAIL>',
        password_hash=generate_password_hash('admin123'),
        is_admin=True
    )
    
    db.session.add(admin)
    db.session.commit()
    print("管理员用户创建完成！用户名: admin, 密码: admin123")

if __name__ == '__main__':
    # 开发环境启动配置
    app.run(
        host='0.0.0.0',  # 允许外部访问
        port=5000,
        debug=True if config_name == 'development' else False,
        threaded=True
    )
