/*******************************************************************************
 * 串口通信类
 * 功能: 与单片机进行串口通信
 ******************************************************************************/

using System;
using System.IO.Ports;
using System.Text;
using System.Threading;

namespace LaserEngraverControl
{
    /// <summary>
    /// 串口通信类
    /// </summary>
    public class SerialCommunication
    {
        #region 私有字段
        private SerialPort serialPort;
        private bool isConnected = false;
        private readonly object lockObject = new object();
        #endregion

        #region 事件定义
        public event EventHandler<string> DataReceived;
        public event EventHandler<string> ErrorOccurred;
        public event EventHandler ConnectionChanged;
        #endregion

        #region 属性
        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected
        {
            get { return isConnected; }
        }

        /// <summary>
        /// 当前端口名
        /// </summary>
        public string PortName
        {
            get { return serialPort?.PortName ?? ""; }
        }
        #endregion

        #region 构造函数
        /// <summary>
        /// 构造函数
        /// </summary>
        public SerialCommunication()
        {
            InitializeSerialPort();
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 初始化串口
        /// </summary>
        private void InitializeSerialPort()
        {
            serialPort = new SerialPort();
            serialPort.BaudRate = 9600;
            serialPort.DataBits = 8;
            serialPort.StopBits = StopBits.One;
            serialPort.Parity = Parity.None;
            serialPort.Handshake = Handshake.None;
            serialPort.ReadTimeout = 1000;
            serialPort.WriteTimeout = 1000;
            serialPort.DataReceived += SerialPort_DataReceived;
            serialPort.ErrorReceived += SerialPort_ErrorReceived;
        }

        /// <summary>
        /// 串口数据接收事件
        /// </summary>
        private void SerialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            try
            {
                string data = serialPort.ReadExisting();
                DataReceived?.Invoke(this, data);
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"数据接收错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 串口错误事件
        /// </summary>
        private void SerialPort_ErrorReceived(object sender, SerialErrorReceivedEventArgs e)
        {
            ErrorOccurred?.Invoke(this, $"串口错误: {e.EventType}");
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 获取可用串口列表
        /// </summary>
        /// <returns>串口名称数组</returns>
        public static string[] GetAvailablePorts()
        {
            return SerialPort.GetPortNames();
        }

        /// <summary>
        /// 连接串口
        /// </summary>
        /// <param name="portName">端口名</param>
        /// <param name="baudRate">波特率</param>
        /// <returns>连接是否成功</returns>
        public bool Connect(string portName, int baudRate = 9600)
        {
            lock (lockObject)
            {
                try
                {
                    if (isConnected)
                    {
                        Disconnect();
                    }

                    serialPort.PortName = portName;
                    serialPort.BaudRate = baudRate;
                    serialPort.Open();
                    isConnected = true;
                    ConnectionChanged?.Invoke(this, EventArgs.Empty);
                    return true;
                }
                catch (Exception ex)
                {
                    ErrorOccurred?.Invoke(this, $"连接失败: {ex.Message}");
                    return false;
                }
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public void Disconnect()
        {
            lock (lockObject)
            {
                try
                {
                    if (serialPort != null && serialPort.IsOpen)
                    {
                        serialPort.Close();
                    }
                    isConnected = false;
                    ConnectionChanged?.Invoke(this, EventArgs.Empty);
                }
                catch (Exception ex)
                {
                    ErrorOccurred?.Invoke(this, $"断开连接失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 发送命令
        /// </summary>
        /// <param name="command">命令字符</param>
        /// <param name="data">数据部分</param>
        /// <returns>发送是否成功</returns>
        public bool SendCommand(char command, string data = "")
        {
            if (!isConnected)
            {
                ErrorOccurred?.Invoke(this, "串口未连接");
                return false;
            }

            try
            {
                // 构建命令格式: <STX><CMD><DATA><ETX><CHK>
                StringBuilder sb = new StringBuilder();
                sb.Append((char)0x02); // STX
                sb.Append(command);    // CMD
                sb.Append(data);       // DATA
                sb.Append((char)0x03); // ETX
                sb.Append("00");       // 简化的校验和

                string commandString = sb.ToString();
                serialPort.Write(commandString);
                return true;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"发送命令失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 发送原始数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <returns>发送是否成功</returns>
        public bool SendRawData(string data)
        {
            if (!isConnected)
            {
                ErrorOccurred?.Invoke(this, "串口未连接");
                return false;
            }

            try
            {
                serialPort.Write(data);
                return true;
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"发送数据失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Disconnect();
            serialPort?.Dispose();
        }
        #endregion
    }
}
