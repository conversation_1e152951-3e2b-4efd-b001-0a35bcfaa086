/*******************************************************************************
 * 通信协议处理模块实现
 * 功能: 处理与上位机的通信协议
 ******************************************************************************/

#include "protocol.h"
#include "uart.h"
#include "stepper.h"
#include "laser.h"
#include "system.h"
#include <stdio.h>
#include <string.h>

/*******************************************************************************
 * 全局变量定义
 ******************************************************************************/
volatile uint8_t protocol_state = PROTOCOL_IDLE;
volatile uint8_t protocol_buffer[PROTOCOL_BUFFER_SIZE];
volatile uint8_t protocol_buffer_index = 0;

/*******************************************************************************
 * 函数: Protocol_Init
 * 功能: 协议初始化
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Protocol_Init(void)
{
    protocol_state = PROTOCOL_IDLE;
    protocol_buffer_index = 0;
}

/*******************************************************************************
 * 函数: Protocol_ProcessCommand
 * 功能: 处理接收到的命令
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Protocol_ProcessCommand(void)
{
    uint8_t byte;
    
    // 检查是否有数据可读
    while(UART_DataAvailable())
    {
        byte = UART_ReceiveByte();
        
        switch(protocol_state)
        {
            case PROTOCOL_IDLE:
                if(byte == STX)
                {
                    protocol_state = PROTOCOL_RECEIVING;
                    protocol_buffer_index = 0;
                }
                break;
                
            case PROTOCOL_RECEIVING:
                if(byte == ETX)
                {
                    protocol_state = PROTOCOL_PROCESSING;
                    Protocol_ParseCommand();
                    protocol_state = PROTOCOL_IDLE;
                }
                else if(protocol_buffer_index < PROTOCOL_BUFFER_SIZE - 1)
                {
                    protocol_buffer[protocol_buffer_index++] = byte;
                }
                else
                {
                    // 缓冲区溢出，重置状态
                    protocol_state = PROTOCOL_IDLE;
                    Protocol_SendError(ERR_FORMAT);
                }
                break;
        }
    }
}

/*******************************************************************************
 * 函数: Protocol_ParseCommand
 * 功能: 解析并执行命令
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
uint8_t Protocol_ParseCommand(void)
{
    uint8_t cmd;
    uint16_t x, y;
    int16_t dx, dy;
    uint8_t power, speed;
    char* data_ptr;
    
    if(protocol_buffer_index < 1)
    {
        Protocol_SendError(ERR_FORMAT);
        return 0;
    }
    
    cmd = protocol_buffer[0];
    data_ptr = (char*)&protocol_buffer[1];
    
    switch(cmd)
    {
        case CMD_MOVE_TO:  // M<X坐标>,<Y坐标>
            if(sscanf(data_ptr, "%u,%u", &x, &y) == 2)
            {
                Stepper_MoveTo(STEPPER_X_AXIS, x);
                Stepper_MoveTo(STEPPER_Y_AXIS, y);
                Protocol_SendResponse(RESP_OK);
            }
            else
            {
                Protocol_SendError(ERR_PARAM);
            }
            break;
            
        case CMD_MOVE_REL:  // R<X偏移>,<Y偏移>
            if(sscanf(data_ptr, "%d,%d", &dx, &dy) == 2)
            {
                Stepper_MoveRelative(STEPPER_X_AXIS, dx);
                Stepper_MoveRelative(STEPPER_Y_AXIS, dy);
                Protocol_SendResponse(RESP_OK);
            }
            else
            {
                Protocol_SendError(ERR_PARAM);
            }
            break;
            
        case CMD_SET_SPEED:  // V<速度值>
            if(sscanf(data_ptr, "%u", &speed) == 1)
            {
                if(speed >= MIN_SPEED && speed <= MAX_SPEED)
                {
                    Stepper_SetSpeed(speed);
                    Protocol_SendResponse(RESP_OK);
                }
                else
                {
                    Protocol_SendError(ERR_PARAM);
                }
            }
            else
            {
                Protocol_SendError(ERR_PARAM);
            }
            break;
            
        case CMD_LASER_ON:  // L<功率值>
            if(sscanf(data_ptr, "%u", &power) == 1)
            {
                if(power <= LASER_MAX_POWER)
                {
                    Laser_SetPower(power);
                    Laser_On();
                    Protocol_SendResponse(RESP_OK);
                }
                else
                {
                    Protocol_SendError(ERR_PARAM);
                }
            }
            else
            {
                Protocol_SendError(ERR_PARAM);
            }
            break;
            
        case CMD_LASER_OFF:  // O
            Laser_Off();
            Protocol_SendResponse(RESP_OK);
            break;
            
        case CMD_RESET:  // Z
            System_Reset();
            Protocol_SendResponse(RESP_OK);
            break;
            
        case CMD_PAUSE:  // P
            System_Pause();
            Protocol_SendResponse(RESP_OK);
            break;
            
        case CMD_CONTINUE:  // C
            System_Continue();
            Protocol_SendResponse(RESP_OK);
            break;
            
        case CMD_STOP:  // S
            System_Stop();
            Protocol_SendResponse(RESP_OK);
            break;
            
        case CMD_QUERY_POS:  // Q
            Protocol_SendPosition();
            break;
            
        case CMD_QUERY_STA:  // T
            Protocol_SendStatus();
            break;
            
        default:
            Protocol_SendError(ERR_UNKNOWN);
            break;
    }
    
    return 1;
}

/*******************************************************************************
 * 函数: Protocol_SendResponse
 * 功能: 发送响应
 * 参数: response - 响应字符串
 * 返回: 无
 ******************************************************************************/
void Protocol_SendResponse(char* response)
{
    UART_SendByte(STX);
    UART_SendString(response);
    UART_SendByte(ETX);
    // 这里应该计算并发送校验和，简化处理
    UART_SendString("00");  // 简化的校验和
}

/*******************************************************************************
 * 函数: Protocol_SendError
 * 功能: 发送错误响应
 * 参数: error_code - 错误码
 * 返回: 无
 ******************************************************************************/
void Protocol_SendError(char* error_code)
{
    UART_SendByte(STX);
    UART_SendString(RESP_ERR);
    UART_SendString(error_code);
    UART_SendByte(ETX);
    UART_SendString("00");  // 简化的校验和
}

/*******************************************************************************
 * 函数: Protocol_SendPosition
 * 功能: 发送位置信息
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Protocol_SendPosition(void)
{
    char buffer[32];
    uint16_t x, y;
    
    x = Stepper_GetPosition(STEPPER_X_AXIS);
    y = Stepper_GetPosition(STEPPER_Y_AXIS);
    
    sprintf(buffer, "X:%u,Y:%u", x, y);
    
    UART_SendByte(STX);
    UART_SendString(RESP_OK);
    UART_SendString(buffer);
    UART_SendByte(ETX);
    UART_SendString("00");  // 简化的校验和
}

/*******************************************************************************
 * 函数: Protocol_SendStatus
 * 功能: 发送状态信息
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Protocol_SendStatus(void)
{
    char buffer[32];
    extern volatile uint8_t system_state;
    
    sprintf(buffer, "STA:%u,PWR:%u,SPD:%u", 
            system_state, Laser_GetPower(), stepper_speed);
    
    UART_SendByte(STX);
    UART_SendString(RESP_STA);
    UART_SendString(buffer);
    UART_SendByte(ETX);
    UART_SendString("00");  // 简化的校验和
}

/*******************************************************************************
 * 函数: Protocol_CalculateChecksum
 * 功能: 计算数据校验和
 * 参数: data - 数据指针, len - 数据长度
 * 返回: 校验和
 ******************************************************************************/
uint8_t Protocol_CalculateChecksum(uint8_t* data, uint8_t len)
{
    uint8_t checksum = 0;
    uint8_t i;

    for(i = 0; i < len; i++)
    {
        checksum ^= data[i];  // 简单的异或校验
    }

    return checksum;
}
