"""
文件上传路由
"""

import os
import uuid
from datetime import datetime
from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from app import db, limiter
from app.models import UploadFile, ProcessingRecord
from app.services.file_handler import FileHandler

upload_bp = Blueprint('upload', __name__)

def allowed_file(filename):
    """检查文件类型是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def get_file_size(file):
    """获取文件大小"""
    file.seek(0, 2)  # 移动到文件末尾
    size = file.tell()
    file.seek(0)     # 重置到文件开头
    return size

@upload_bp.route('/')
@login_required
def upload_page():
    """文件上传页面"""
    return render_template('upload/upload.html')

@upload_bp.route('/upload', methods=['POST'])
@login_required
@limiter.limit("10 per minute")
def upload_file():
    """处理文件上传"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'}), 400
        
        # 验证文件类型
        if not allowed_file(file.filename):
            allowed_types = ', '.join(current_app.config['ALLOWED_EXTENSIONS'])
            return jsonify({
                'success': False, 
                'message': f'不支持的文件类型。支持的类型：{allowed_types}'
            }), 400
        
        # 检查文件大小
        file_size = get_file_size(file)
        max_size = current_app.config['MAX_CONTENT_LENGTH']
        if file_size > max_size:
            max_mb = max_size // (1024 * 1024)
            return jsonify({
                'success': False, 
                'message': f'文件过大。最大支持 {max_mb}MB'
            }), 400
        
        # 生成安全的文件名
        original_filename = file.filename
        file_extension = original_filename.rsplit('.', 1)[1].lower()
        safe_filename = f"{uuid.uuid4().hex}.{file_extension}"
        
        # 确保上传目录存在
        upload_folder = current_app.config['UPLOAD_FOLDER']
        os.makedirs(upload_folder, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(upload_folder, safe_filename)
        file.save(file_path)
        
        # 验证文件内容
        file_handler = FileHandler()
        validation_result = file_handler.validate_file(file_path, file_extension)
        
        if not validation_result['valid']:
            # 删除无效文件
            os.remove(file_path)
            return jsonify({
                'success': False, 
                'message': f'文件验证失败：{validation_result["message"]}'
            }), 400
        
        # 保存文件记录到数据库
        upload_file_record = UploadFile(
            filename=safe_filename,
            original_filename=original_filename,
            file_path=file_path,
            file_size=file_size,
            file_type=file_extension,
            user_id=current_user.id,
            status='uploaded'
        )
        
        db.session.add(upload_file_record)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'file_id': upload_file_record.id,
            'filename': original_filename,
            'file_size': file_size,
            'preview_data': validation_result.get('preview_data', {})
        })
        
    except Exception as e:
        current_app.logger.error(f"文件上传错误: {str(e)}")
        return jsonify({'success': False, 'message': '文件上传失败，请稍后重试'}), 500

@upload_bp.route('/batch')
@login_required
def batch_upload_page():
    """批量上传页面"""
    return render_template('upload/batch_upload.html')

@upload_bp.route('/batch_upload', methods=['POST'])
@login_required
@limiter.limit("5 per minute")
def batch_upload():
    """批量文件上传"""
    try:
        files = request.files.getlist('files')
        if not files or len(files) == 0:
            return jsonify({'success': False, 'message': '没有选择文件'}), 400
        
        max_batch_size = current_app.config.get('MAX_BATCH_SIZE', 10)
        if len(files) > max_batch_size:
            return jsonify({
                'success': False, 
                'message': f'批量上传最多支持 {max_batch_size} 个文件'
            }), 400
        
        uploaded_files = []
        failed_files = []
        
        for file in files:
            if file.filename == '':
                continue
                
            try:
                # 验证文件
                if not allowed_file(file.filename):
                    failed_files.append({
                        'filename': file.filename,
                        'reason': '不支持的文件类型'
                    })
                    continue
                
                file_size = get_file_size(file)
                max_size = current_app.config['MAX_CONTENT_LENGTH']
                if file_size > max_size:
                    failed_files.append({
                        'filename': file.filename,
                        'reason': '文件过大'
                    })
                    continue
                
                # 保存文件
                original_filename = file.filename
                file_extension = original_filename.rsplit('.', 1)[1].lower()
                safe_filename = f"{uuid.uuid4().hex}.{file_extension}"
                
                upload_folder = current_app.config['UPLOAD_FOLDER']
                file_path = os.path.join(upload_folder, safe_filename)
                file.save(file_path)
                
                # 验证文件内容
                file_handler = FileHandler()
                validation_result = file_handler.validate_file(file_path, file_extension)
                
                if not validation_result['valid']:
                    os.remove(file_path)
                    failed_files.append({
                        'filename': original_filename,
                        'reason': validation_result['message']
                    })
                    continue
                
                # 保存到数据库
                upload_file_record = UploadFile(
                    filename=safe_filename,
                    original_filename=original_filename,
                    file_path=file_path,
                    file_size=file_size,
                    file_type=file_extension,
                    user_id=current_user.id,
                    status='uploaded'
                )
                
                db.session.add(upload_file_record)
                db.session.commit()
                
                uploaded_files.append({
                    'id': upload_file_record.id,
                    'filename': original_filename,
                    'size': file_size
                })
                
            except Exception as e:
                failed_files.append({
                    'filename': file.filename,
                    'reason': f'处理失败: {str(e)}'
                })
        
        return jsonify({
            'success': True,
            'message': f'批量上传完成。成功: {len(uploaded_files)}, 失败: {len(failed_files)}',
            'uploaded_files': uploaded_files,
            'failed_files': failed_files
        })
        
    except Exception as e:
        current_app.logger.error(f"批量上传错误: {str(e)}")
        return jsonify({'success': False, 'message': '批量上传失败，请稍后重试'}), 500

@upload_bp.route('/files')
@login_required
def list_files():
    """获取用户上传的文件列表"""
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('POSTS_PER_PAGE', 20)
    
    files = UploadFile.query.filter_by(user_id=current_user.id)\
                           .order_by(UploadFile.upload_time.desc())\
                           .paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('upload/file_list.html', files=files)

@upload_bp.route('/files/<int:file_id>')
@login_required
def file_detail(file_id):
    """文件详情"""
    file_record = UploadFile.query.filter_by(id=file_id, user_id=current_user.id).first_or_404()
    
    # 获取相关的处理记录
    processing_records = ProcessingRecord.query.filter_by(file_id=file_id)\
                                              .order_by(ProcessingRecord.created_at.desc())\
                                              .all()
    
    return render_template('upload/file_detail.html', 
                         file=file_record, 
                         processing_records=processing_records)

@upload_bp.route('/files/<int:file_id>/delete', methods=['POST'])
@login_required
def delete_file(file_id):
    """删除文件"""
    file_record = UploadFile.query.filter_by(id=file_id, user_id=current_user.id).first_or_404()
    
    try:
        # 删除物理文件
        if os.path.exists(file_record.file_path):
            os.remove(file_record.file_path)
        
        # 删除相关的处理记录
        ProcessingRecord.query.filter_by(file_id=file_id).delete()
        
        # 删除数据库记录
        db.session.delete(file_record)
        db.session.commit()
        
        flash('文件删除成功', 'success')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除文件错误: {str(e)}")
        flash('文件删除失败', 'error')
    
    return redirect(url_for('upload.list_files'))
