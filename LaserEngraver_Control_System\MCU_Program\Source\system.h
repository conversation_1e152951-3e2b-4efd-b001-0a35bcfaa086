/*******************************************************************************
 * 系统管理模块头文件
 * 功能: 系统初始化、状态管理、安全保护
 ******************************************************************************/

#ifndef __SYSTEM_H__
#define __SYSTEM_H__

#include "STC15W201S.h"

/*******************************************************************************
 * 函数声明
 ******************************************************************************/
void System_Init(void);                 // 系统初始化
void System_Reset(void);                // 系统复位
void System_Pause(void);                // 系统暂停
void System_Continue(void);             // 系统恢复
void System_Stop(void);                 // 系统停止
void System_Monitor(void);              // 系统监控
void System_WatchdogFeed(void);         // 看门狗喂狗
void System_EmergencyStop(void);        // 紧急停止
uint8_t System_GetState(void);          // 获取系统状态

#endif // __SYSTEM_H__
