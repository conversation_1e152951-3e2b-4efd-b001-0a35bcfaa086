# 激光雕刻机控制系统用户手册

## 目录
1. [系统概述](#1-系统概述)
2. [安装指南](#2-安装指南)
3. [快速开始](#3-快速开始)
4. [功能详解](#4-功能详解)
5. [文件格式支持](#5-文件格式支持)
6. [故障排除](#6-故障排除)
7. [安全注意事项](#7-安全注意事项)
8. [技术支持](#8-技术支持)

## 1. 系统概述

### 1.1 产品介绍
激光雕刻机控制系统是一套完整的激光雕刻解决方案，包含单片机控制程序和Windows上位机软件。系统支持多种文件格式，具有路径优化、实时监控等功能。

### 1.2 主要特性
- **多格式支持**: G-code、DXF、SVG文件格式
- **实时控制**: 激光功率、运动速度实时调节
- **路径优化**: 自动优化雕刻路径，提高效率
- **安全保护**: 多重安全机制，确保操作安全
- **用户友好**: 直观的图形界面，易于操作

### 1.3 技术规格
- **控制芯片**: STC15W201S
- **通信方式**: 串口通信 (9600bps)
- **运动控制**: 双轴步进电机
- **激光控制**: PWM功率调节 (0-100%)
- **定位精度**: ±0.1mm
- **支持系统**: Windows 7/8/10/11

## 2. 安装指南

### 2.1 硬件安装
1. **单片机连接**
   - 将STC15W201S单片机按照引脚定义连接
   - 连接USB转串口模块
   - 连接步进电机驱动器
   - 连接激光模块

2. **电源连接**
   - 单片机供电: 5V
   - 步进电机供电: 12V (根据电机规格)
   - 激光模块供电: 按模块要求

3. **安全检查**
   - 确保所有连接牢固
   - 检查电源极性
   - 验证接地连接

### 2.2 软件安装
1. **上位机软件**
   - 运行 `LaserEngraverControl.exe`
   - 首次运行会自动检测.NET Framework
   - 如需要，请安装.NET Framework 4.7.2或更高版本

2. **驱动程序**
   - 安装USB转串口驱动程序
   - 确认设备管理器中串口正常识别

## 3. 快速开始

### 3.1 首次使用
1. **连接硬件**
   - 将USB转串口连接到电脑
   - 给系统上电
   - 确认所有指示灯正常

2. **启动软件**
   - 双击运行 `LaserEngraverControl.exe`
   - 软件界面分为控制面板和预览区域

3. **建立连接**
   - 在串口连接区域选择正确的COM端口
   - 波特率选择9600
   - 点击"连接"按钮
   - 连接成功后状态栏显示"已连接"

### 3.2 第一次雕刻
1. **加载文件**
   - 点击"浏览"按钮选择测试文件
   - 推荐使用提供的 `test_square.gcode`
   - 预览窗口会显示雕刻路径

2. **设置参数**
   - 激光功率: 建议从30%开始测试
   - 运动速度: 建议50%
   - 确认工件位置

3. **开始雕刻**
   - 点击"开始雕刻"按钮
   - 观察雕刻过程
   - 可随时暂停或停止

## 4. 功能详解

### 4.1 串口连接
- **端口选择**: 自动检测可用串口
- **波特率**: 固定9600bps，与单片机匹配
- **连接状态**: 实时显示连接状态
- **自动重连**: 连接断开时提示重连

### 4.2 运动控制
- **手动定位**: 输入坐标直接移动
- **速度控制**: 1-100%速度调节
- **回零功能**: 一键返回原点
- **停止功能**: 紧急停止所有运动

### 4.3 激光控制
- **功率调节**: 0-100%精确控制
- **开关控制**: 独立的激光开关
- **安全保护**: 异常时自动关闭激光
- **实时反馈**: 显示当前激光状态

### 4.4 文件操作
- **文件加载**: 支持多种格式
- **路径预览**: 图形化显示雕刻路径
- **路径优化**: 自动优化提高效率
- **进度显示**: 实时显示雕刻进度

### 4.5 雕刻控制
- **开始雕刻**: 执行完整雕刻流程
- **暂停恢复**: 可暂停并恢复雕刻
- **停止功能**: 立即停止雕刻
- **状态监控**: 实时显示系统状态

## 5. 文件格式支持

### 5.1 G-code文件 (.gcode, .nc)
**支持的命令:**
- `G0/G00`: 快速移动 (激光关闭)
- `G1/G01`: 直线插补 (激光开启)
- `M3`: 激光开启
- `M5`: 激光关闭
- `F`: 进给速度设置
- `S`: 激光功率设置

**示例文件:**
```gcode
G0 X0 Y0        ; 移动到原点
M3 S50          ; 激光开启，功率50%
G1 X100 Y0 F500 ; 直线到(100,0)
G1 X100 Y100    ; 直线到(100,100)
M5              ; 激光关闭
```

### 5.2 DXF文件 (.dxf)
- 支持基本几何图形
- 自动转换为雕刻路径
- 保持原始尺寸比例

### 5.3 SVG文件 (.svg)
- 支持路径元素
- 自动解析坐标信息
- 转换为直线段

## 6. 故障排除

### 6.1 连接问题
**问题**: 无法连接串口
**解决方案**:
1. 检查串口号是否正确
2. 确认驱动程序已安装
3. 检查其他程序是否占用串口
4. 重新插拔USB连接

**问题**: 连接后无响应
**解决方案**:
1. 检查单片机程序是否正确下载
2. 确认波特率设置正确
3. 检查硬件连接
4. 重启系统

### 6.2 运动问题
**问题**: 步进电机不转动
**解决方案**:
1. 检查电机驱动器连接
2. 确认电源电压正确
3. 检查相序连接
4. 验证控制信号

**问题**: 运动不准确
**解决方案**:
1. 检查机械传动系统
2. 调整步进电机参数
3. 校准坐标系统
4. 检查软件设置

### 6.3 激光问题
**问题**: 激光不工作
**解决方案**:
1. 检查激光模块连接
2. 确认电源供电正常
3. 验证PWM信号
4. 检查安全开关

**问题**: 激光功率不准确
**解决方案**:
1. 校准PWM输出
2. 检查激光模块规格
3. 调整软件参数
4. 测量实际功率

## 7. 安全注意事项

### 7.1 激光安全
⚠️ **警告**: 激光可能对眼睛和皮肤造成伤害

- 佩戴适当的激光防护眼镜
- 避免直视激光束
- 确保工作区域通风良好
- 使用防火材料作为工作台面
- 配备灭火器材

### 7.2 电气安全
- 确保正确接地
- 使用合适的电源适配器
- 避免在潮湿环境中操作
- 定期检查线路连接

### 7.3 机械安全
- 保持工作区域整洁
- 避免在运动部件附近放置物品
- 定期检查机械部件
- 使用急停开关

## 8. 技术支持

### 8.1 常见问题
请查看FAQ部分或访问技术支持网站获取最新信息。

### 8.2 联系方式
- 技术支持邮箱: <EMAIL>
- 用户手册更新: 请定期检查软件更新

### 8.3 版本信息
- 当前版本: v1.0.0
- 发布日期: 2025-07-20
- 兼容性: Windows 7/8/10/11

---

**免责声明**: 请在使用前仔细阅读所有安全注意事项。制造商不对因不当使用造成的任何损失承担责任。
