# SDCC编译器使用说明

## 📁 目录结构检查

请确认您的SDCC目录结构如下：
```
D:\sdcc\
├── bin\
│   ├── sdcc.exe          ← 主编译器
│   ├── packihx.exe       ← HEX文件打包工具
│   ├── makebin.exe       ← 二进制文件生成工具
│   └── ...
├── include\              ← 头文件目录
│   ├── mcs51\
│   └── ...
└── lib\                  ← 库文件目录
    ├── small\
    └── ...
```

## 🚀 快速开始

### 方法一：运行配置脚本（推荐）
1. 双击运行 `setup_sdcc.bat`
2. 脚本会自动检查和配置SDCC
3. 选择是否立即编译项目

### 方法二：手动编译
1. 打开命令提示符
2. 运行以下命令：
```cmd
cd /d "D:\Code_POR\LaserEngraver_Control_System\MCU_Program_SDCC"
build.bat
```

## 🔧 环境变量设置（可选）

如果希望永久使用SDCC，可以将以下路径添加到系统PATH环境变量：
```
D:\sdcc\bin
```

### 设置步骤：
1. 右键"此电脑" → "属性"
2. 点击"高级系统设置"
3. 点击"环境变量"
4. 在"系统变量"中找到"Path"
5. 点击"编辑" → "新建"
6. 添加：`D:\sdcc\bin`
7. 确定保存

## 📝 编译输出

编译成功后会生成：
- `LaserEngraver.hex` - 用于烧录到单片机的HEX文件
- `LaserEngraver.ihx` - Intel HEX格式文件
- `*.rel` - 目标文件
- `*.lst` - 汇编列表文件

## 🔍 故障排除

### 问题1：找不到sdcc.exe
**解决方案：**
- 检查D:\sdcc目录是否存在
- 确认bin子目录中有sdcc.exe文件

### 问题2：编译错误
**解决方案：**
- 检查源代码语法
- 确认所有头文件都存在
- 查看错误信息并修复

### 问题3：生成的HEX文件无法烧录
**解决方案：**
- 使用STC-ISP工具烧录
- 确认单片机型号匹配（STC15W201S）
- 检查HEX文件大小是否合理

## 📞 需要帮助？

如果遇到问题，请提供：
1. 错误信息截图
2. SDCC目录结构
3. 编译日志

我会帮您解决！
