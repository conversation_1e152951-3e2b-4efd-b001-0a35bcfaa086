/*******************************************************************************
 * 通信协议处理模块头文件
 * 功能: 处理与上位机的通信协议
 ******************************************************************************/

#ifndef __PROTOCOL_H__
#define __PROTOCOL_H__

#include "STC15W201S.h"

/*******************************************************************************
 * 协议定义
 ******************************************************************************/
#define STX             0x02    // 起始字符
#define ETX             0x03    // 结束字符

// 命令定义
#define CMD_MOVE_TO     'M'     // 移动到指定位置
#define CMD_MOVE_REL    'R'     // 相对移动
#define CMD_SET_SPEED   'V'     // 设置速度
#define CMD_LASER_ON    'L'     // 激光开启
#define CMD_LASER_OFF   'O'     // 激光关闭
#define CMD_RESET       'Z'     // 复位
#define CMD_PAUSE       'P'     // 暂停
#define CMD_CONTINUE    'C'     // 恢复
#define CMD_STOP        'S'     // 停止
#define CMD_QUERY_POS   'Q'     // 查询位置
#define CMD_QUERY_STA   'T'     // 查询状态

// 响应定义
#define RESP_OK         "OK"    // 成功响应
#define RESP_ERR        "ERR"   // 错误响应
#define RESP_STA        "STA"   // 状态响应

// 错误码定义
#define ERR_FORMAT      "01"    // 命令格式错误
#define ERR_CHECKSUM    "02"    // 校验和错误
#define ERR_PARAM       "03"    // 参数超出范围
#define ERR_BUSY        "04"    // 系统忙碌
#define ERR_HARDWARE    "05"    // 硬件故障
#define ERR_UNKNOWN     "06"    // 未知命令

/*******************************************************************************
 * 协议状态定义
 ******************************************************************************/
#define PROTOCOL_IDLE       0   // 空闲状态
#define PROTOCOL_RECEIVING  1   // 接收状态
#define PROTOCOL_PROCESSING 2   // 处理状态

/*******************************************************************************
 * 缓冲区大小
 ******************************************************************************/
#define PROTOCOL_BUFFER_SIZE    32

/*******************************************************************************
 * 函数声明
 ******************************************************************************/
void Protocol_Init(void);                      // 协议初始化
void Protocol_ProcessCommand(void);            // 处理命令
void Protocol_SendResponse(char *response);    // 发送响应
void Protocol_SendError(char *error_code);     // 发送错误响应
void Protocol_SendStatus(void);                // 发送状态信息
void Protocol_SendPosition(void);              // 发送位置信息
uint8_t Protocol_CalculateChecksum(uint8_t *data, uint8_t len); // 计算校验和
uint8_t Protocol_ParseCommand(void);           // 解析命令

/*******************************************************************************
 * 全局变量声明
 ******************************************************************************/
extern volatile uint8_t protocol_state;
extern volatile uint8_t protocol_buffer[PROTOCOL_BUFFER_SIZE];
extern volatile uint8_t protocol_buffer_index;

#endif // __PROTOCOL_H__
