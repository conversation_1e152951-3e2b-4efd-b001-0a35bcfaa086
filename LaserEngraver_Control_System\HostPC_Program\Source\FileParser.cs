/*******************************************************************************
 * 文件解析器类
 * 功能: 解析DXF、SVG、G-code等文件格式
 ******************************************************************************/

using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text.RegularExpressions;

namespace LaserEngraverControl
{
    /// <summary>
    /// 路径点类型
    /// </summary>
    public enum PathPointType
    {
        MoveTo,     // 移动到指定位置 (激光关闭)
        LineTo,     // 直线到指定位置 (激光开启)
        ArcTo       // 圆弧到指定位置 (激光开启)
    }

    /// <summary>
    /// 路径点结构
    /// </summary>
    public struct PathPoint
    {
        public PathPointType Type;
        public PointF Position;
        public int LaserPower;  // 激光功率 (0-100)
        public float Speed;     // 速度 (0-100)

        public PathPoint(PathPointType type, PointF position, int laserPower = 0, float speed = 50)
        {
            Type = type;
            Position = position;
            LaserPower = laserPower;
            Speed = speed;
        }
    }

    /// <summary>
    /// 文件解析器类
    /// </summary>
    public class FileParser
    {
        #region 事件定义
        public event EventHandler<string> StatusChanged;
        public event EventHandler<int> ProgressChanged;
        #endregion

        #region 公共方法
        /// <summary>
        /// 解析文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>路径点列表</returns>
        public List<PathPoint> ParseFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException($"文件不存在: {filePath}");
            }

            string extension = Path.GetExtension(filePath).ToLower();
            
            StatusChanged?.Invoke(this, $"开始解析文件: {Path.GetFileName(filePath)}");

            switch (extension)
            {
                case ".gcode":
                case ".nc":
                    return ParseGCode(filePath);
                case ".dxf":
                    return ParseDXF(filePath);
                case ".svg":
                    return ParseSVG(filePath);
                default:
                    throw new NotSupportedException($"不支持的文件格式: {extension}");
            }
        }

        /// <summary>
        /// 解析G-code文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>路径点列表</returns>
        public List<PathPoint> ParseGCode(string filePath)
        {
            List<PathPoint> pathPoints = new List<PathPoint>();
            string[] lines = File.ReadAllLines(filePath);
            
            PointF currentPosition = new PointF(0, 0);
            int currentLaserPower = 0;
            float currentSpeed = 50;
            bool laserOn = false;

            for (int i = 0; i < lines.Length; i++)
            {
                string line = lines[i].Trim().ToUpper();
                
                // 更新进度
                ProgressChanged?.Invoke(this, (i * 100) / lines.Length);

                // 跳过注释和空行
                if (string.IsNullOrEmpty(line) || line.StartsWith(";") || line.StartsWith("("))
                    continue;

                try
                {
                    // 解析G-code命令
                    if (line.StartsWith("G0") || line.StartsWith("G00")) // 快速移动
                    {
                        PointF newPos = ParseCoordinates(line, currentPosition);
                        pathPoints.Add(new PathPoint(PathPointType.MoveTo, newPos, 0, 100));
                        currentPosition = newPos;
                        laserOn = false;
                    }
                    else if (line.StartsWith("G1") || line.StartsWith("G01")) // 直线插补
                    {
                        PointF newPos = ParseCoordinates(line, currentPosition);
                        int power = laserOn ? currentLaserPower : 0;
                        pathPoints.Add(new PathPoint(PathPointType.LineTo, newPos, power, currentSpeed));
                        currentPosition = newPos;
                    }
                    else if (line.StartsWith("M3")) // 激光开启
                    {
                        laserOn = true;
                        // 检查是否有S参数 (激光功率)
                        Match match = Regex.Match(line, @"S(\d+(?:\.\d+)?)");
                        if (match.Success)
                        {
                            float power = float.Parse(match.Groups[1].Value);
                            currentLaserPower = (int)Math.Min(100, power); // 假设最大值为100
                        }
                    }
                    else if (line.StartsWith("M5")) // 激光关闭
                    {
                        laserOn = false;
                        currentLaserPower = 0;
                    }
                    else if (line.StartsWith("F")) // 进给速度
                    {
                        Match match = Regex.Match(line, @"F(\d+(?:\.\d+)?)");
                        if (match.Success)
                        {
                            currentSpeed = Math.Min(100, float.Parse(match.Groups[1].Value) / 10); // 简化处理
                        }
                    }
                }
                catch (Exception ex)
                {
                    StatusChanged?.Invoke(this, $"解析第{i+1}行时出错: {ex.Message}");
                }
            }

            StatusChanged?.Invoke(this, $"G-code解析完成，共{pathPoints.Count}个路径点");
            ProgressChanged?.Invoke(this, 100);
            return pathPoints;
        }

        /// <summary>
        /// 解析DXF文件 (简化实现)
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>路径点列表</returns>
        public List<PathPoint> ParseDXF(string filePath)
        {
            List<PathPoint> pathPoints = new List<PathPoint>();
            
            // 这里是DXF解析的简化实现
            // 实际项目中需要使用专门的DXF解析库
            StatusChanged?.Invoke(this, "DXF解析功能需要进一步开发");
            
            // 示例：创建一个简单的矩形
            pathPoints.Add(new PathPoint(PathPointType.MoveTo, new PointF(10, 10), 0));
            pathPoints.Add(new PathPoint(PathPointType.LineTo, new PointF(100, 10), 80));
            pathPoints.Add(new PathPoint(PathPointType.LineTo, new PointF(100, 100), 80));
            pathPoints.Add(new PathPoint(PathPointType.LineTo, new PointF(10, 100), 80));
            pathPoints.Add(new PathPoint(PathPointType.LineTo, new PointF(10, 10), 80));
            pathPoints.Add(new PathPoint(PathPointType.MoveTo, new PointF(0, 0), 0));

            return pathPoints;
        }

        /// <summary>
        /// 解析SVG文件 (简化实现)
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>路径点列表</returns>
        public List<PathPoint> ParseSVG(string filePath)
        {
            List<PathPoint> pathPoints = new List<PathPoint>();
            
            // 这里是SVG解析的简化实现
            // 实际项目中需要使用专门的SVG解析库
            StatusChanged?.Invoke(this, "SVG解析功能需要进一步开发");
            
            // 示例：创建一个简单的圆形路径
            float centerX = 50, centerY = 50, radius = 30;
            int segments = 36;
            
            for (int i = 0; i <= segments; i++)
            {
                double angle = (2 * Math.PI * i) / segments;
                float x = centerX + (float)(radius * Math.Cos(angle));
                float y = centerY + (float)(radius * Math.Sin(angle));
                
                PathPointType type = (i == 0) ? PathPointType.MoveTo : PathPointType.LineTo;
                int power = (i == 0) ? 0 : 80;
                
                pathPoints.Add(new PathPoint(type, new PointF(x, y), power));
            }

            return pathPoints;
        }

        /// <summary>
        /// 从G-code行中解析坐标
        /// </summary>
        /// <param name="line">G-code行</param>
        /// <param name="currentPos">当前位置</param>
        /// <returns>新位置</returns>
        private PointF ParseCoordinates(string line, PointF currentPos)
        {
            float x = currentPos.X;
            float y = currentPos.Y;

            // 解析X坐标
            Match xMatch = Regex.Match(line, @"X(-?\d+(?:\.\d+)?)");
            if (xMatch.Success)
            {
                x = float.Parse(xMatch.Groups[1].Value);
            }

            // 解析Y坐标
            Match yMatch = Regex.Match(line, @"Y(-?\d+(?:\.\d+)?)");
            if (yMatch.Success)
            {
                y = float.Parse(yMatch.Groups[1].Value);
            }

            return new PointF(x, y);
        }

        /// <summary>
        /// 验证文件格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否支持该格式</returns>
        public static bool IsSupportedFormat(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            return extension == ".gcode" || extension == ".nc" || 
                   extension == ".dxf" || extension == ".svg";
        }

        /// <summary>
        /// 获取支持的文件格式过滤器
        /// </summary>
        /// <returns>文件对话框过滤器字符串</returns>
        public static string GetFileFilter()
        {
            return "支持的文件|*.gcode;*.nc;*.dxf;*.svg|" +
                   "G-code文件|*.gcode;*.nc|" +
                   "DXF文件|*.dxf|" +
                   "SVG文件|*.svg|" +
                   "所有文件|*.*";
        }
        #endregion
    }
}
