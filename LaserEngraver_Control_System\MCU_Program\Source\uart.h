/*******************************************************************************
 * 串口通信模块头文件
 * 功能: 实现与上位机的串口通信
 ******************************************************************************/

#ifndef __UART_H__
#define __UART_H__

#include "STC15W201S.h"

/*******************************************************************************
 * 宏定义
 ******************************************************************************/
#define UART_BUFFER_SIZE    64      // 串口缓冲区大小
#define UART_TIMEOUT        1000    // 超时时间(ms)

/*******************************************************************************
 * 函数声明
 ******************************************************************************/
void UART_Init(void);                           // 串口初始化
void UART_SendByte(uint8_t byte);              // 发送单字节
void UART_SendString(char* str);               // 发送字符串
void UART_SendData(uint8_t* data, uint8_t len); // 发送数据
uint8_t UART_ReceiveByte(void);                // 接收单字节
uint8_t UART_DataAvailable(void);              // 检查是否有数据
void UART_ReceiveISR(void);                    // 接收中断服务程序
void UART_TransmitISR(void);                   // 发送中断服务程序
void UART_ClearBuffer(void);                   // 清空接收缓冲区

/*******************************************************************************
 * 全局变量声明
 ******************************************************************************/
extern volatile uint8_t uart_rx_buffer[UART_BUFFER_SIZE];
extern volatile uint8_t uart_rx_head;
extern volatile uint8_t uart_rx_tail;
extern volatile uint8_t uart_tx_buffer[UART_BUFFER_SIZE];
extern volatile uint8_t uart_tx_head;
extern volatile uint8_t uart_tx_tail;
extern volatile uint8_t uart_tx_busy;

#endif // __UART_H__
