Java课程作业源代码集合
====================

题目1：复数类的实现
==================

// 复数类
class Complex {
    private double real;    // 实部
    private double imaginary;  // 虚部
    
    // 构造方法
    public Complex(double real, double imaginary) {
        this.real = real;
        this.imaginary = imaginary;
    }
    
    // 默认构造方法
    public Complex() {
        this(0, 0);
    }
    
    // 复数加法
    public Complex add(Complex other) {
        return new Complex(this.real + other.real, this.imaginary + other.imaginary);
    }
    
    // 复数减法
    public Complex subtract(Complex other) {
        return new Complex(this.real - other.real, this.imaginary - other.imaginary);
    }
    
    // 复数乘法
    public Complex multiply(Complex other) {
        double newReal = this.real * other.real - this.imaginary * other.imaginary;
        double newImaginary = this.real * other.imaginary + this.imaginary * other.real;
        return new Complex(newReal, newImaginary);
    }
    
    // 复数除法
    public Complex divide(Complex other) {
        double denominator = other.real * other.real + other.imaginary * other.imaginary;
        if (denominator == 0) {
            throw new ArithmeticException("除数不能为零");
        }
        double newReal = (this.real * other.real + this.imaginary * other.imaginary) / denominator;
        double newImaginary = (this.imaginary * other.real - this.real * other.imaginary) / denominator;
        return new Complex(newReal, newImaginary);
    }
    
    // 获取实部
    public double getReal() {
        return real;
    }
    
    // 获取虚部
    public double getImaginary() {
        return imaginary;
    }
    
    // 重写toString方法
    @Override
    public String toString() {
        if (imaginary >= 0) {
            return real + " + " + imaginary + "i";
        } else {
            return real + " - " + Math.abs(imaginary) + "i";
        }
    }
}

// 测试类
public class ComplexTest {
    public static void main(String[] args) {
        // 创建两个复数
        Complex c1 = new Complex(3, 4);
        Complex c2 = new Complex(1, 2);
        
        System.out.println("复数1: " + c1);
        System.out.println("复数2: " + c2);
        
        // 测试加法
        Complex sum = c1.add(c2);
        System.out.println("加法结果: " + sum);
        
        // 测试减法
        Complex difference = c1.subtract(c2);
        System.out.println("减法结果: " + difference);
        
        // 测试乘法
        Complex product = c1.multiply(c2);
        System.out.println("乘法结果: " + product);
        
        // 测试除法
        Complex quotient = c1.divide(c2);
        System.out.println("除法结果: " + quotient);
    }
}

题目2：普通类、匿名类和Lambda表达式实现
=====================================

// 接口定义
interface Computable {
    int compute(int x, int y);
}

// 方式1：普通类实现
class Add implements Computable {
    @Override
    public int compute(int x, int y) {
        return x + y;
    }
}

// 主类
public class ComputeExample {
    public static void main(String[] args) {
        int x = 10, y = 20;
        
        // 方式1：使用普通类
        Computable comp1 = new Add();
        System.out.println("普通类实现: " + comp1.compute(x, y));
        
        // 方式2：使用匿名类
        Computable comp2 = new Computable() {
            @Override
            public int compute(int x, int y) {
                return x + y;
            }
        };
        System.out.println("匿名类实现: " + comp2.compute(x, y));
        
        // 方式3：使用Lambda表达式
        Computable comp3 = (a, b) -> a + b;
        System.out.println("Lambda表达式实现: " + comp3.compute(x, y));
        
        /*
         * Lambda表达式理解：
         * 1. Lambda表达式是Java 8引入的新特性，用于简化函数式接口的实现
         * 2. 语法：(参数列表) -> {方法体}
         * 3. 当方法体只有一行时，可以省略大括号和return关键字
         * 4. Lambda表达式本质上是匿名函数，可以作为参数传递
         * 5. 相比匿名类，Lambda表达式更简洁，代码可读性更好
         * 6. Lambda表达式只能用于函数式接口（只有一个抽象方法的接口）
         */
    }
}

题目3：P244，6 - 异常处理
=======================

// 自定义异常类
class InvalidAgeException extends Exception {
    public InvalidAgeException(String message) {
        super(message);
    }
}

// 人员类
class Person {
    private String name;
    private int age;
    
    public Person(String name, int age) throws InvalidAgeException {
        this.name = name;
        setAge(age);
    }
    
    public void setAge(int age) throws InvalidAgeException {
        if (age < 0 || age > 150) {
            throw new InvalidAgeException("年龄必须在0-150之间，当前年龄：" + age);
        }
        this.age = age;
    }
    
    public int getAge() {
        return age;
    }
    
    public String getName() {
        return name;
    }
    
    @Override
    public String toString() {
        return "姓名：" + name + "，年龄：" + age;
    }
}

public class ExceptionExample {
    public static void main(String[] args) {
        try {
            // 正常情况
            Person person1 = new Person("张三", 25);
            System.out.println("创建成功：" + person1);
            
            // 异常情况1：年龄为负数
            Person person2 = new Person("李四", -5);
            System.out.println("创建成功：" + person2);
            
        } catch (InvalidAgeException e) {
            System.out.println("捕获异常：" + e.getMessage());
        }
        
        try {
            // 异常情况2：年龄超过150
            Person person3 = new Person("王五", 200);
            System.out.println("创建成功：" + person3);
            
        } catch (InvalidAgeException e) {
            System.out.println("捕获异常：" + e.getMessage());
        }
        
        // 使用finally块
        try {
            Person person4 = new Person("赵六", 30);
            person4.setAge(160);  // 这里会抛出异常
        } catch (InvalidAgeException e) {
            System.out.println("设置年龄异常：" + e.getMessage());
        } finally {
            System.out.println("finally块总是会执行");
        }
    }
}

题目4：P302，2 - 集合框架
=======================

import java.util.*;

public class CollectionExample {
    public static void main(String[] args) {
        // 1. ArrayList示例
        System.out.println("=== ArrayList示例 ===");
        List<String> arrayList = new ArrayList<>();
        arrayList.add("苹果");
        arrayList.add("香蕉");
        arrayList.add("橙子");
        arrayList.add("苹果");  // 允许重复元素

        System.out.println("ArrayList内容：" + arrayList);
        System.out.println("第二个元素：" + arrayList.get(1));
        System.out.println("苹果的索引：" + arrayList.indexOf("苹果"));

        // 2. LinkedList示例
        System.out.println("\n=== LinkedList示例 ===");
        LinkedList<Integer> linkedList = new LinkedList<>();
        linkedList.addFirst(1);
        linkedList.addLast(3);
        linkedList.add(1, 2);  // 在索引1处插入2

        System.out.println("LinkedList内容：" + linkedList);
        System.out.println("第一个元素：" + linkedList.getFirst());
        System.out.println("最后一个元素：" + linkedList.getLast());

        // 3. HashSet示例
        System.out.println("\n=== HashSet示例 ===");
        Set<String> hashSet = new HashSet<>();
        hashSet.add("Java");
        hashSet.add("Python");
        hashSet.add("C++");
        hashSet.add("Java");  // 重复元素不会被添加

        System.out.println("HashSet内容：" + hashSet);
        System.out.println("是否包含Java：" + hashSet.contains("Java"));

        // 4. TreeSet示例（自动排序）
        System.out.println("\n=== TreeSet示例 ===");
        Set<Integer> treeSet = new TreeSet<>();
        treeSet.add(5);
        treeSet.add(2);
        treeSet.add(8);
        treeSet.add(1);

        System.out.println("TreeSet内容（自动排序）：" + treeSet);

        // 5. HashMap示例
        System.out.println("\n=== HashMap示例 ===");
        Map<String, Integer> hashMap = new HashMap<>();
        hashMap.put("张三", 85);
        hashMap.put("李四", 92);
        hashMap.put("王五", 78);

        System.out.println("HashMap内容：" + hashMap);
        System.out.println("张三的成绩：" + hashMap.get("张三"));

        // 遍历HashMap
        System.out.println("遍历HashMap：");
        for (Map.Entry<String, Integer> entry : hashMap.entrySet()) {
            System.out.println(entry.getKey() + ": " + entry.getValue());
        }

        // 6. 集合的常用操作
        System.out.println("\n=== 集合操作示例 ===");
        List<String> list1 = Arrays.asList("A", "B", "C");
        List<String> list2 = Arrays.asList("B", "C", "D");

        // 并集
        Set<String> union = new HashSet<>(list1);
        union.addAll(list2);
        System.out.println("并集：" + union);

        // 交集
        Set<String> intersection = new HashSet<>(list1);
        intersection.retainAll(list2);
        System.out.println("交集：" + intersection);

        // 差集
        Set<String> difference = new HashSet<>(list1);
        difference.removeAll(list2);
        System.out.println("差集：" + difference);
    }
}

题目5：P346，2 - 多线程
=====================

// 方式1：继承Thread类
class MyThread extends Thread {
    private String threadName;

    public MyThread(String name) {
        this.threadName = name;
    }

    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            System.out.println(threadName + " - 计数: " + i);
            try {
                Thread.sleep(1000);  // 休眠1秒
            } catch (InterruptedException e) {
                System.out.println(threadName + " 被中断");
            }
        }
        System.out.println(threadName + " 执行完毕");
    }
}

// 方式2：实现Runnable接口
class MyRunnable implements Runnable {
    private String threadName;

    public MyRunnable(String name) {
        this.threadName = name;
    }

    @Override
    public void run() {
        for (int i = 1; i <= 5; i++) {
            System.out.println(threadName + " - 计数: " + i);
            try {
                Thread.sleep(800);  // 休眠0.8秒
            } catch (InterruptedException e) {
                System.out.println(threadName + " 被中断");
            }
        }
        System.out.println(threadName + " 执行完毕");
    }
}

// 线程同步示例
class Counter {
    private int count = 0;

    // 同步方法
    public synchronized void increment() {
        count++;
        System.out.println(Thread.currentThread().getName() + " - 当前计数: " + count);
    }

    public int getCount() {
        return count;
    }
}

public class ThreadExample {
    public static void main(String[] args) {
        System.out.println("=== 多线程示例 ===");

        // 方式1：继承Thread类
        MyThread thread1 = new MyThread("线程1");
        MyThread thread2 = new MyThread("线程2");

        thread1.start();
        thread2.start();

        // 方式2：实现Runnable接口
        Thread thread3 = new Thread(new MyRunnable("线程3"));
        Thread thread4 = new Thread(new MyRunnable("线程4"));

        thread3.start();
        thread4.start();

        // 方式3：使用Lambda表达式
        Thread thread5 = new Thread(() -> {
            for (int i = 1; i <= 3; i++) {
                System.out.println("Lambda线程 - 计数: " + i);
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        });
        thread5.start();

        // 线程同步示例
        System.out.println("\n=== 线程同步示例 ===");
        Counter counter = new Counter();

        // 创建多个线程同时访问共享资源
        for (int i = 1; i <= 3; i++) {
            Thread t = new Thread(() -> {
                for (int j = 0; j < 3; j++) {
                    counter.increment();
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }, "计数线程" + i);
            t.start();
        }
    }
}

题目6：P486，3 - 网络编程
=======================

import java.io.*;
import java.net.*;

// 简单的TCP服务器
class SimpleServer {
    private ServerSocket serverSocket;
    private int port;

    public SimpleServer(int port) {
        this.port = port;
    }

    public void start() {
        try {
            serverSocket = new ServerSocket(port);
            System.out.println("服务器启动，监听端口：" + port);

            while (true) {
                // 等待客户端连接
                Socket clientSocket = serverSocket.accept();
                System.out.println("客户端连接：" + clientSocket.getInetAddress());

                // 处理客户端请求（在新线程中）
                new Thread(() -> handleClient(clientSocket)).start();
            }
        } catch (IOException e) {
            System.out.println("服务器启动失败：" + e.getMessage());
        }
    }

    private void handleClient(Socket clientSocket) {
        try (BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
             PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true)) {

            String inputLine;
            while ((inputLine = in.readLine()) != null) {
                System.out.println("收到消息：" + inputLine);

                // 回显消息给客户端
                out.println("服务器回复：" + inputLine);

                // 如果收到"bye"则断开连接
                if ("bye".equalsIgnoreCase(inputLine)) {
                    break;
                }
            }
        } catch (IOException e) {
            System.out.println("处理客户端请求时出错：" + e.getMessage());
        } finally {
            try {
                clientSocket.close();
                System.out.println("客户端连接已关闭");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void stop() {
        try {
            if (serverSocket != null) {
                serverSocket.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}

// 简单的TCP客户端
class SimpleClient {
    private String hostname;
    private int port;

    public SimpleClient(String hostname, int port) {
        this.hostname = hostname;
        this.port = port;
    }

    public void connect() {
        try (Socket socket = new Socket(hostname, port);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()));
             BufferedReader stdIn = new BufferedReader(new InputStreamReader(System.in))) {

            System.out.println("连接到服务器：" + hostname + ":" + port);
            System.out.println("输入消息（输入'bye'退出）：");

            String userInput;
            while ((userInput = stdIn.readLine()) != null) {
                // 发送消息到服务器
                out.println(userInput);

                // 接收服务器回复
                String response = in.readLine();
                System.out.println(response);

                // 如果输入bye则退出
                if ("bye".equalsIgnoreCase(userInput)) {
                    break;
                }
            }
        } catch (IOException e) {
            System.out.println("客户端连接失败：" + e.getMessage());
        }
    }
}

// UDP通信示例
class UDPExample {
    // UDP服务器
    public static void startUDPServer(int port) {
        new Thread(() -> {
            try (DatagramSocket socket = new DatagramSocket(port)) {
                System.out.println("UDP服务器启动，监听端口：" + port);

                byte[] buffer = new byte[1024];

                while (true) {
                    // 接收数据包
                    DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
                    socket.receive(packet);

                    String received = new String(packet.getData(), 0, packet.getLength());
                    System.out.println("UDP服务器收到：" + received);

                    // 回复客户端
                    String response = "UDP服务器回复：" + received;
                    byte[] responseData = response.getBytes();
                    DatagramPacket responsePacket = new DatagramPacket(
                        responseData, responseData.length,
                        packet.getAddress(), packet.getPort());
                    socket.send(responsePacket);
                }
            } catch (IOException e) {
                System.out.println("UDP服务器错误：" + e.getMessage());
            }
        }).start();
    }

    // UDP客户端
    public static void sendUDPMessage(String hostname, int port, String message) {
        try (DatagramSocket socket = new DatagramSocket()) {
            InetAddress address = InetAddress.getByName(hostname);

            // 发送数据包
            byte[] data = message.getBytes();
            DatagramPacket packet = new DatagramPacket(data, data.length, address, port);
            socket.send(packet);

            // 接收回复
            byte[] buffer = new byte[1024];
            DatagramPacket responsePacket = new DatagramPacket(buffer, buffer.length);
            socket.receive(responsePacket);

            String response = new String(responsePacket.getData(), 0, responsePacket.getLength());
            System.out.println("UDP客户端收到：" + response);

        } catch (IOException e) {
            System.out.println("UDP客户端错误：" + e.getMessage());
        }
    }
}

public class NetworkExample {
    public static void main(String[] args) {
        System.out.println("=== 网络编程示例 ===");

        // 演示TCP通信
        System.out.println("1. TCP通信示例");

        // 启动TCP服务器（在新线程中）
        new Thread(() -> {
            SimpleServer server = new SimpleServer(8080);
            server.start();
        }).start();

        // 等待服务器启动
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 演示UDP通信
        System.out.println("\n2. UDP通信示例");

        // 启动UDP服务器
        UDPExample.startUDPServer(8081);

        // 等待UDP服务器启动
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        // 发送UDP消息
        UDPExample.sendUDPMessage("localhost", 8081, "Hello UDP Server!");

        System.out.println("\n网络编程示例运行中...");
        System.out.println("注意：实际使用时需要分别运行服务器和客户端程序");

        /*
         * 网络编程要点：
         * 1. TCP是面向连接的可靠协议，适用于需要可靠传输的场景
         * 2. UDP是无连接的不可靠协议，适用于对实时性要求高的场景
         * 3. 服务器需要监听指定端口，客户端连接到服务器的IP和端口
         * 4. 多线程处理可以让服务器同时处理多个客户端连接
         * 5. 异常处理很重要，网络编程中经常出现IO异常
         */
    }
}

===========================================
作业完成说明：
===========================================

本文档包含了Java课程的6道作业题的完整源代码：

1. 复数类：实现了复数的加、减、乘、除运算，包含测试类
2. 三种实现方式：普通类、匿名类、Lambda表达式，并详细说明了Lambda表达式的理解
3. 异常处理：自定义异常类，演示异常的抛出和捕获
4. 集合框架：演示ArrayList、LinkedList、HashSet、TreeSet、HashMap的使用
5. 多线程：演示继承Thread类、实现Runnable接口、Lambda表达式创建线程，以及线程同步
6. 网络编程：实现简单的TCP和UDP通信示例

每个题目都包含了详细的注释说明，代码结构清晰，便于理解和学习。

注意：网络编程示例中的服务器和客户端代码在实际使用时需要分别运行。
