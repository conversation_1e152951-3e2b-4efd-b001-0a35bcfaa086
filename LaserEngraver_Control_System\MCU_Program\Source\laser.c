/*******************************************************************************
 * 激光控制模块实现
 * 功能: 控制激光器的开关和功率
 ******************************************************************************/

#include "laser.h"

/*******************************************************************************
 * 全局变量定义
 ******************************************************************************/
volatile uint8_t laser_power = 0;          // 当前激光功率 (0-100)
volatile uint8_t laser_state = LASER_OFF;  // 激光状态
volatile uint8_t laser_pwm_counter = 0;    // PWM计数器

/*******************************************************************************
 * 函数: Laser_Init
 * 功能: 激光器初始化
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Laser_Init(void)
{
    // 初始化激光控制引脚
    LASER_PWM = 0;  // PWM输出初始为低电平
    LASER_EN = 0;   // 激光使能初始为关闭
    
    // 初始化变量
    laser_power = 0;
    laser_state = LASER_OFF;
    laser_pwm_counter = 0;
}

/*******************************************************************************
 * 函数: Laser_SetPower
 * 功能: 设置激光功率
 * 参数: power - 功率百分比 (0-100)
 * 返回: 无
 ******************************************************************************/
void Laser_SetPower(uint8_t power)
{
    // 限制功率范围
    if(power > LASER_MAX_POWER)
        power = LASER_MAX_POWER;
    
    laser_power = power;
    
    // 如果功率为0，关闭激光
    if(power == 0)
    {
        Laser_Off();
    }
    else if(laser_state == LASER_OFF)
    {
        // 如果激光处于关闭状态但设置了功率，则开启激光
        Laser_On();
    }
}

/*******************************************************************************
 * 函数: Laser_On
 * 功能: 开启激光
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Laser_On(void)
{
    if(laser_power > 0)
    {
        laser_state = LASER_ON;
        LASER_EN = 1;  // 使能激光器
    }
}

/*******************************************************************************
 * 函数: Laser_Off
 * 功能: 关闭激光
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Laser_Off(void)
{
    laser_state = LASER_OFF;
    LASER_EN = 0;    // 禁用激光器
    LASER_PWM = 0;   // PWM输出为低电平
}

/*******************************************************************************
 * 函数: Laser_GetPower
 * 功能: 获取当前激光功率
 * 参数: 无
 * 返回: 当前功率百分比
 ******************************************************************************/
uint8_t Laser_GetPower(void)
{
    return laser_power;
}

/*******************************************************************************
 * 函数: Laser_GetState
 * 功能: 获取激光状态
 * 参数: 无
 * 返回: 激光状态 (LASER_ON/LASER_OFF)
 ******************************************************************************/
uint8_t Laser_GetState(void)
{
    return laser_state;
}

/*******************************************************************************
 * 函数: Laser_Process
 * 功能: 激光处理函数 (主循环调用)
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Laser_Process(void)
{
    // 安全检查 - 如果系统处于错误状态，关闭激光
    extern volatile uint8_t system_state;
    if(system_state == SYS_ERROR)
    {
        Laser_Off();
    }
}

/*******************************************************************************
 * 函数: Laser_TimerISR
 * 功能: 定时器中断服务程序 - 生成PWM信号
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Laser_TimerISR(void)
{
    // PWM信号生成 (软件PWM)
    if(laser_state == LASER_ON)
    {
        laser_pwm_counter++;
        
        // PWM周期为100个计数 (对应100%功率)
        if(laser_pwm_counter >= PWM_RESOLUTION)
        {
            laser_pwm_counter = 0;
        }
        
        // 根据功率设置PWM输出
        if(laser_pwm_counter < laser_power)
        {
            LASER_PWM = 1;  // 高电平
        }
        else
        {
            LASER_PWM = 0;  // 低电平
        }
    }
    else
    {
        LASER_PWM = 0;      // 激光关闭时PWM为低电平
        laser_pwm_counter = 0;
    }
}
