# 激光雕刻机控制系统安装指南

## 目录
1. [系统要求](#1-系统要求)
2. [硬件安装](#2-硬件安装)
3. [软件安装](#3-软件安装)
4. [配置设置](#4-配置设置)
5. [测试验证](#5-测试验证)
6. [故障排除](#6-故障排除)

## 1. 系统要求

### 1.1 硬件要求
**最低配置:**
- CPU: Intel Pentium 4 或 AMD 同等处理器
- 内存: 2GB RAM
- 硬盘: 500MB 可用空间
- 显示器: 1024x768 分辨率
- USB端口: 至少1个可用USB 2.0端口

**推荐配置:**
- CPU: Intel Core i3 或 AMD 同等处理器
- 内存: 4GB RAM 或更多
- 硬盘: 1GB 可用空间
- 显示器: 1920x1080 分辨率
- USB端口: USB 3.0端口

### 1.2 软件要求
- **操作系统**: Windows 7 SP1 / 8.1 / 10 / 11 (32位或64位)
- **.NET Framework**: 4.7.2 或更高版本
- **USB驱动**: CH340/CP2102/FT232等USB转串口驱动

### 1.3 开发环境要求 (仅开发者)
- **单片机开发**: Keil uVision 5
- **上位机开发**: Visual Studio 2019/2022
- **版本控制**: Git (可选)

## 2. 硬件安装

### 2.1 组件清单
确保您有以下组件:
- [ ] STC15W201S单片机开发板
- [ ] USB转串口模块 (CH340/CP2102/FT232)
- [ ] 步进电机驱动器 x2
- [ ] 步进电机 x2 (NEMA17推荐)
- [ ] 激光模块 (可选，测试时可用LED代替)
- [ ] 电源适配器 (5V/12V)
- [ ] 连接线材
- [ ] 机械结构件

### 2.2 电路连接

#### 2.2.1 单片机引脚连接
```
STC15W201S引脚分配:
┌─────────────────────────────────────┐
│  功能        │ 引脚 │ 端口 │ 连接目标 │
├─────────────────────────────────────┤
│ 串口接收     │  9   │ P3.0 │ USB转串口TX │
│ 串口发送     │ 10   │ P3.1 │ USB转串口RX │
│ X轴A相       │ 15   │ P1.0 │ 驱动器IN1   │
│ X轴B相       │ 16   │ P1.1 │ 驱动器IN2   │
│ X轴C相       │  1   │ P1.2 │ 驱动器IN3   │
│ X轴D相       │  2   │ P1.3 │ 驱动器IN4   │
│ Y轴A相       │ 11   │ P3.2 │ 驱动器IN1   │
│ Y轴B相       │ 12   │ P3.3 │ 驱动器IN2   │
│ Y轴C相       │ 13   │ P3.6 │ 驱动器IN3   │
│ Y轴D相       │ 14   │ P3.7 │ 驱动器IN4   │
│ 激光PWM      │  -   │ P2.0 │ 激光模块PWM │
│ 激光使能     │  -   │ P2.1 │ 激光模块EN  │
│ 电源正极     │  -   │ VCC  │ +5V         │
│ 电源负极     │  -   │ GND  │ GND         │
└─────────────────────────────────────┘
```

#### 2.2.2 步进电机驱动器连接
**ULN2003驱动器 (推荐用于测试):**
```
单片机端口 -> ULN2003 -> 步进电机
P1.0/P3.2  -> IN1      -> 线圈A+
P1.1/P3.3  -> IN2      -> 线圈A-
P1.2/P3.6  -> IN3      -> 线圈B+
P1.3/P3.7  -> IN4      -> 线圈B-
```

**专用步进驱动器 (生产环境推荐):**
```
单片机端口 -> 驱动器
P1.0/P3.2  -> PUL+ (脉冲信号)
P1.1/P3.3  -> DIR+ (方向信号)
P1.2/P3.6  -> ENA+ (使能信号)
GND        -> PUL-/DIR-/ENA-
```

#### 2.2.3 激光模块连接
```
单片机端口 -> 激光模块
P2.0       -> PWM输入 (功率控制)
P2.1       -> EN输入  (使能控制)
+12V       -> VCC     (电源正极)
GND        -> GND     (电源负极)
```

### 2.3 安全检查
安装完成后进行以下检查:
- [ ] 所有连接牢固可靠
- [ ] 电源极性正确
- [ ] 接地连接良好
- [ ] 无短路现象
- [ ] 激光模块安全防护到位

## 3. 软件安装

### 3.1 驱动程序安装

#### 3.1.1 USB转串口驱动
1. **自动安装** (Windows 10/11):
   - 连接USB转串口模块到电脑
   - 系统会自动搜索并安装驱动
   - 在设备管理器中确认串口设备

2. **手动安装** (Windows 7/8):
   - 下载对应芯片的驱动程序:
     - CH340: [官方下载链接]
     - CP2102: [官方下载链接]
     - FT232: [官方下载链接]
   - 运行驱动安装程序
   - 重启计算机

#### 3.1.2 验证驱动安装
1. 打开设备管理器 (Win+X -> 设备管理器)
2. 展开"端口(COM和LPT)"
3. 确认看到类似"USB-SERIAL CH340 (COM3)"的设备
4. 记录COM端口号，后续软件配置需要

### 3.2 上位机软件安装

#### 3.2.1 .NET Framework检查
1. 按Win+R，输入`cmd`，按回车
2. 输入命令: `reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" /v Release`
3. 如果返回值 >= 461808，则已安装.NET Framework 4.7.2
4. 如果未安装，请从Microsoft官网下载安装

#### 3.2.2 软件部署
**方式一: 直接运行 (推荐)**
1. 解压软件包到目标目录 (如: `C:\LaserEngraver\`)
2. 双击运行 `LaserEngraverControl.exe`
3. 首次运行可能需要管理员权限

**方式二: 编译安装 (开发者)**
1. 安装Visual Studio 2019/2022
2. 打开项目文件 `LaserEngraverControl.csproj`
3. 选择Release配置
4. 生成解决方案 (Ctrl+Shift+B)
5. 在bin\Release目录找到可执行文件

### 3.3 单片机程序烧录

#### 3.3.1 准备工作
1. 下载STC-ISP软件 (STC官网免费下载)
2. 安装并运行STC-ISP
3. 准备HEX文件: `MCU_Program\Keil_Project\Objects\LaserEngraver.hex`

#### 3.3.2 烧录步骤
1. **连接硬件**:
   - 将USB转串口连接到电脑
   - 连接单片机的串口引脚
   - 给单片机上电

2. **配置STC-ISP**:
   - 选择芯片型号: STC15W201S
   - 选择串口号 (与USB转串口对应)
   - 波特率选择: 9600 或自动检测
   - 加载HEX文件

3. **开始烧录**:
   - 点击"下载/编程"按钮
   - 按提示给单片机断电再上电
   - 等待烧录完成
   - 验证烧录结果

## 4. 配置设置

### 4.1 首次运行配置
1. **启动软件**:
   - 双击运行 `LaserEngraverControl.exe`
   - 软件界面应正常显示

2. **串口配置**:
   - 在串口连接区域选择正确的COM端口
   - 波特率保持9600 (与单片机匹配)
   - 点击"连接"按钮测试连接

3. **参数设置**:
   - 激光功率: 建议初始值30% (安全测试)
   - 运动速度: 建议初始值50%
   - 坐标范围: 根据实际机械结构设置

### 4.2 系统校准
1. **原点校准**:
   - 手动移动到机械原点位置
   - 在软件中点击"回零"按钮
   - 确认坐标显示为(0,0)

2. **运动测试**:
   - 输入小幅度坐标 (如10,10)
   - 点击"移动到"按钮
   - 观察实际运动是否与预期一致

3. **激光测试**:
   - 设置低功率 (如10%)
   - 点击"开启"按钮
   - 确认激光模块响应正常
   - 立即点击"关闭"按钮

## 5. 测试验证

### 5.1 通信测试
1. 连接串口后观察状态栏
2. 发送查询位置命令 (软件自动)
3. 检查日志窗口的通信记录
4. 确认命令响应正常

### 5.2 运动测试
1. 加载测试文件: `test_square.gcode`
2. 在预览窗口确认路径显示
3. 设置安全参数 (低功率、低速度)
4. 执行测试雕刻
5. 观察运动轨迹是否正确

### 5.3 完整功能测试
1. 测试所有控制按钮
2. 测试暂停/恢复功能
3. 测试停止功能
4. 测试文件加载功能
5. 验证安全保护机制

## 6. 故障排除

### 6.1 连接问题
**问题**: 找不到串口设备
**解决**: 
- 检查USB连接
- 重新安装驱动程序
- 尝试不同的USB端口

**问题**: 连接超时
**解决**:
- 检查波特率设置
- 确认单片机程序正确烧录
- 检查串口线连接

### 6.2 运动问题
**问题**: 电机不转动
**解决**:
- 检查驱动器连接
- 确认电源供电
- 验证控制信号

**问题**: 运动方向错误
**解决**:
- 调整相序连接
- 修改软件参数
- 检查机械传动

### 6.3 激光问题
**问题**: 激光无响应
**解决**:
- 检查激光模块连接
- 确认PWM信号输出
- 验证电源供电

### 6.4 软件问题
**问题**: 程序无法启动
**解决**:
- 检查.NET Framework版本
- 以管理员身份运行
- 检查防病毒软件设置

**问题**: 界面显示异常
**解决**:
- 调整显示分辨率
- 更新显卡驱动
- 重新安装软件

## 7. 维护建议

### 7.1 定期检查
- 每周检查连接线路
- 每月清洁机械部件
- 每季度校准坐标系统

### 7.2 软件更新
- 定期检查软件更新
- 备份重要配置文件
- 记录系统变更日志

### 7.3 安全维护
- 定期检查激光安全设施
- 更新安全操作规程
- 培训操作人员

---

**安装完成后，请仔细阅读用户手册和安全注意事项！**
