"""
路由模块初始化
"""

from flask import Blueprint, render_template, redirect, url_for
from flask_login import login_required, current_user

# 创建主蓝图
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """首页"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return render_template('index.html')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """用户仪表板"""
    from app.models import UploadFile, ProcessingRecord
    
    # 获取用户的文件和处理记录统计
    total_files = UploadFile.query.filter_by(user_id=current_user.id).count()
    total_records = ProcessingRecord.query.filter_by(user_id=current_user.id).count()
    
    # 最近的文件
    recent_files = UploadFile.query.filter_by(user_id=current_user.id)\
                                  .order_by(UploadFile.upload_time.desc())\
                                  .limit(5).all()
    
    # 最近的处理记录
    recent_records = ProcessingRecord.query.filter_by(user_id=current_user.id)\
                                          .order_by(ProcessingRecord.created_at.desc())\
                                          .limit(5).all()
    
    return render_template('dashboard.html',
                         total_files=total_files,
                         total_records=total_records,
                         recent_files=recent_files,
                         recent_records=recent_records)

@main_bp.route('/about')
def about():
    """关于页面"""
    return render_template('about.html')

@main_bp.route('/help')
def help():
    """帮助页面"""
    return render_template('help.html')
