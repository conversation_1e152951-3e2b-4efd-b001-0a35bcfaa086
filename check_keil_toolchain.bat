@echo off
echo ========================================
echo Keil工具链检查脚本
echo ========================================

echo 检查Keil安装目录...
if exist "C:\Keil_v5\" (
    echo ✓ 找到Keil安装目录: C:\Keil_v5\
    
    echo.
    echo 检查C51编译器...
    if exist "C:\Keil_v5\C51\BIN\C51.EXE" (
        echo ✓ 找到C51编译器: C:\Keil_v5\C51\BIN\C51.EXE
        
        echo.
        echo C51编译器版本信息:
        "C:\Keil_v5\C51\BIN\C51.EXE" 2>&1 | findstr "Version"
        
    ) else (
        echo ✗ 未找到C51编译器
        echo   需要安装C51编译器工具链
    )
    
    echo.
    echo 检查ARM编译器...
    if exist "C:\Keil_v5\ARM\ARMCC\bin\armcc.exe" (
        echo ✓ 找到ARM编译器
    ) else (
        echo ✗ 未找到ARM编译器
    )
    
) else (
    echo ✗ 未找到Keil安装目录
    echo   请检查Keil是否正确安装
)

echo.
echo 检查SDCC编译器（备选方案）...
where sdcc >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ 找到SDCC编译器
    sdcc --version
) else (
    echo ✗ 未找到SDCC编译器
)

echo.
echo ========================================
echo 检查完成
echo ========================================
pause
