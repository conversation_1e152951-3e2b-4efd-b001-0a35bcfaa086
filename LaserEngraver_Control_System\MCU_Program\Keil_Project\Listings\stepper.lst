C51 COMPILER V9.01   STEPPER                                                               07/20/2025 12:44:14 PAGE 1   


C51 COMPILER V9.01, COMPILATION OF MODULE STEPPER
OBJECT MODULE PLACED IN .\Objects\stepper.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\Source\stepper.c BROWSE INCDIR(..\Source) DEBUG OBJECTEXTEND PRINT(.\Lis
                    -tings\stepper.lst) OBJECT(.\Objects\stepper.obj)

line level    source

*** WARNING C500 IN LINE 1 OF ..\SOURCE\STEPPER.C: LICENSE ERROR (R208: RENEW LICENSE ID CODE (LIC))

   1          /*******************************************************************************
   2           * 步进电机控制模块实现
   3           * 功能: 控制X轴和Y轴步进电机运动
   4           ******************************************************************************/
   5          
   6          #include "stepper.h"
   7          
   8          /*******************************************************************************
   9           * 全局变量定义
  10           ******************************************************************************/
  11          StepperMotor_t stepper_x;
  12          StepperMotor_t stepper_y;
  13          volatile uint8_t stepper_speed = 50;  // 默认速度50%
  14          
  15          /*******************************************************************************
  16           * 步进电机相位表 (四相八拍)
  17           ******************************************************************************/
  18          code uint8_t stepper_phase_table[8] = {
  19              0x01,  // 0001 - A相
  20              0x03,  // 0011 - A+B相
  21              0x02,  // 0010 - B相
  22              0x06,  // 0110 - B+C相
  23              0x04,  // 0100 - C相
  24              0x0C,  // 1100 - C+D相
  25              0x08,  // 1000 - D相
  26              0x09   // 1001 - D+A相
  27          };
  28          
  29          /*******************************************************************************
  30           * 函数: Stepper_Init
  31           * 功能: 步进电机初始化
  32           * 参数: 无
  33           * 返回: 无
  34           ******************************************************************************/
  35          void Stepper_Init(void)
  36          {
  37   1          // 初始化X轴步进电机
  38   1          stepper_x.target_pos = 0;
  39   1          stepper_x.current_pos = 0;
  40   1          stepper_x.direction = STEPPER_DIR_CW;
  41   1          stepper_x.state = STEPPER_STATE_IDLE;
  42   1          stepper_x.step_count = 0;
  43   1          stepper_x.step_phase = 0;
  44   1          stepper_x.step_delay = 100;  // 默认延时
  45   1          stepper_x.step_timer = 0;
  46   1          
  47   1          // 初始化Y轴步进电机
  48   1          stepper_y.target_pos = 0;
  49   1          stepper_y.current_pos = 0;
  50   1          stepper_y.direction = STEPPER_DIR_CW;
  51   1          stepper_y.state = STEPPER_STATE_IDLE;
  52   1          stepper_y.step_count = 0;
  53   1          stepper_y.step_phase = 0;
C51 COMPILER V9.01   STEPPER                                                               07/20/2025 12:44:14 PAGE 2   

  54   1          stepper_y.step_delay = 100;  // 默认延时
  55   1          stepper_y.step_timer = 0;
  56   1          
  57   1          // 设置步进电机引脚为输出
  58   1          P1 |= 0x0F;  // X轴引脚设为高电平
  59   1          P3 |= 0xCC;  // Y轴引脚设为高电平 (P3.2,P3.3,P3.6,P3.7)
  60   1          
  61   1          // 初始化定时器0用于步进电机脉冲生成
  62   1          TMOD &= 0xF0;   // 清除定时器0模式位
  63   1          TMOD |= 0x01;   // 定时器0工作在模式1（16位定时器）
  64   1          TH0 = 0xFC;     // 设置初值 (1ms @ 12MHz)
  65   1          TL0 = 0x18;
  66   1          TR0 = 1;        // 启动定时器0
  67   1          ET0 = 1;        // 使能定时器0中断
  68   1      }
  69          
  70          /*******************************************************************************
  71           * 函数: Stepper_MoveTo
  72           * 功能: 移动到指定位置
  73           * 参数: axis - 轴选择, position - 目标位置
  74           * 返回: 无
  75           ******************************************************************************/
  76          void Stepper_MoveTo(uint8_t axis, uint16_t position)
  77          {
  78   1          StepperMotor_t *motor;
  79   1          
  80   1          // 选择电机
  81   1          if(axis == STEPPER_X_AXIS)
  82   1              motor = &stepper_x;
  83   1          else
  84   1              motor = &stepper_y;
  85   1          
  86   1          // 计算运动参数
  87   1          motor->target_pos = position;
  88   1          
  89   1          if(position > motor->current_pos)
  90   1          {
  91   2              motor->direction = STEPPER_DIR_CW;
  92   2              motor->step_count = position - motor->current_pos;
  93   2          }
  94   1          else if(position < motor->current_pos)
  95   1          {
  96   2              motor->direction = STEPPER_DIR_CCW;
  97   2              motor->step_count = motor->current_pos - position;
  98   2          }
  99   1          else
 100   1          {
 101   2              motor->step_count = 0;
 102   2              return;  // 已在目标位置
 103   2          }
 104   1          
 105   1          // 启动运动
 106   1          motor->state = STEPPER_STATE_MOVE;
 107   1          motor->step_timer = 0;
 108   1      }
 109          
 110          /*******************************************************************************
 111           * 函数: Stepper_MoveRelative
 112           * 功能: 相对移动
 113           * 参数: axis - 轴选择, steps - 移动步数(正数为正向，负数为反向)
 114           * 返回: 无
 115           ******************************************************************************/
C51 COMPILER V9.01   STEPPER                                                               07/20/2025 12:44:14 PAGE 3   

 116          void Stepper_MoveRelative(uint8_t axis, int16_t steps)
 117          {
 118   1          StepperMotor_t *motor;
 119   1          uint16_t target_pos;
 120   1          
 121   1          // 选择电机
 122   1          if(axis == STEPPER_X_AXIS)
 123   1              motor = &stepper_x;
 124   1          else
 125   1              motor = &stepper_y;
 126   1          
 127   1          // 计算目标位置
 128   1          if(steps >= 0)
 129   1          {
 130   2              target_pos = motor->current_pos + steps;
 131   2          }
 132   1          else
 133   1          {
 134   2              if(motor->current_pos >= (-steps))
 135   2                  target_pos = motor->current_pos + steps;
 136   2              else
 137   2                  target_pos = 0;
 138   2          }
 139   1          
 140   1          // 执行移动
 141   1          Stepper_MoveTo(axis, target_pos);
 142   1      }
 143          
 144          /*******************************************************************************
 145           * 函数: Stepper_SetSpeed
 146           * 功能: 设置运动速度
 147           * 参数: speed - 速度百分比 (1-100)
 148           * 返回: 无
 149           ******************************************************************************/
 150          void Stepper_SetSpeed(uint8_t speed)
 151          {
 152   1          uint16_t delay;
 153   1          
 154   1          // 限制速度范围
 155   1          if(speed < MIN_SPEED) speed = MIN_SPEED;
 156   1          if(speed > MAX_SPEED) speed = MAX_SPEED;
 157   1          
 158   1          stepper_speed = speed;
 159   1          
 160   1          // 计算步进延时 (速度越高，延时越小)
 161   1          delay = 200 - speed;  // 100-200ms范围
 162   1          
 163   1          stepper_x.step_delay = delay;
 164   1          stepper_y.step_delay = delay;
 165   1      }
 166          
 167          /*******************************************************************************
 168           * 函数: Stepper_Stop
 169           * 功能: 停止电机运动
 170           * 参数: axis - 轴选择
 171           * 返回: 无
 172           ******************************************************************************/
 173          void Stepper_Stop(uint8_t axis)
 174          {
 175   1          if(axis == STEPPER_X_AXIS)
 176   1          {
 177   2              stepper_x.state = STEPPER_STATE_IDLE;
C51 COMPILER V9.01   STEPPER                                                               07/20/2025 12:44:14 PAGE 4   

 178   2              stepper_x.step_count = 0;
 179   2          }
 180   1          else
 181   1          {
 182   2              stepper_y.state = STEPPER_STATE_IDLE;
 183   2              stepper_y.step_count = 0;
 184   2          }
 185   1      }
 186          
 187          /*******************************************************************************
 188           * 函数: Stepper_Process
 189           * 功能: 步进电机处理函数(主循环调用)
 190           * 参数: 无
 191           * 返回: 无
 192           ******************************************************************************/
 193          void Stepper_Process(void)
 194          {
 195   1          // 这里可以添加额外的处理逻辑
 196   1          // 主要的步进控制在定时器中断中完成
 197   1      }
 198          
 199          /*******************************************************************************
 200           * 函数: Stepper_TimerISR
 201           * 功能: 定时器中断服务程序
 202           * 参数: 无
 203           * 返回: 无
 204           ******************************************************************************/
 205          void Stepper_TimerISR(void)
 206          {
 207   1          // 处理X轴步进电机
 208   1          if(stepper_x.state == STEPPER_STATE_MOVE)
 209   1          {
 210   2              stepper_x.step_timer++;
 211   2              if(stepper_x.step_timer >= stepper_x.step_delay)
 212   2              {
 213   3                  stepper_x.step_timer = 0;
 214   3                  Stepper_StepX();
 215   3              }
 216   2          }
 217   1      
 218   1          // 处理Y轴步进电机
 219   1          if(stepper_y.state == STEPPER_STATE_MOVE)
 220   1          {
 221   2              stepper_y.step_timer++;
 222   2              if(stepper_y.step_timer >= stepper_y.step_delay)
 223   2              {
 224   3                  stepper_y.step_timer = 0;
 225   3                  Stepper_StepY();
 226   3              }
 227   2          }
 228   1      }
 229          
 230          /*******************************************************************************
 231           * 函数: Stepper_StepX
 232           * 功能: X轴步进一步
 233           * 参数: 无
 234           * 返回: 无
 235           ******************************************************************************/
 236          void Stepper_StepX(void)
 237          {
 238   1          uint8_t phase;
 239   1      
C51 COMPILER V9.01   STEPPER                                                               07/20/2025 12:44:14 PAGE 5   

 240   1          if(stepper_x.step_count == 0)
 241   1          {
 242   2              stepper_x.state = STEPPER_STATE_IDLE;
 243   2              return;
 244   2          }
 245   1      
 246   1          // 更新相位
 247   1          if(stepper_x.direction == STEPPER_DIR_CW)
 248   1          {
 249   2              stepper_x.step_phase = (stepper_x.step_phase + 1) % 8;
 250   2              stepper_x.current_pos++;
 251   2          }
 252   1          else
 253   1          {
 254   2              stepper_x.step_phase = (stepper_x.step_phase + 7) % 8;
 255   2              if(stepper_x.current_pos > 0)
 256   2                  stepper_x.current_pos--;
 257   2          }
 258   1      
 259   1          // 输出相位到引脚
 260   1          phase = stepper_phase_table[stepper_x.step_phase];
 261   1          P1 = (P1 & 0xF0) | (phase & 0x0F);
 262   1      
 263   1          // 减少剩余步数
 264   1          stepper_x.step_count--;
 265   1      }
 266          
 267          /*******************************************************************************
 268           * 函数: Stepper_StepY
 269           * 功能: Y轴步进一步
 270           * 参数: 无
 271           * 返回: 无
 272           ******************************************************************************/
 273          void Stepper_StepY(void)
 274          {
 275   1          uint8_t phase;
 276   1      
 277   1          if(stepper_y.step_count == 0)
 278   1          {
 279   2              stepper_y.state = STEPPER_STATE_IDLE;
 280   2              return;
 281   2          }
 282   1      
 283   1          // 更新相位
 284   1          if(stepper_y.direction == STEPPER_DIR_CW)
 285   1          {
 286   2              stepper_y.step_phase = (stepper_y.step_phase + 1) % 8;
 287   2              stepper_y.current_pos++;
 288   2          }
 289   1          else
 290   1          {
 291   2              stepper_y.step_phase = (stepper_y.step_phase + 7) % 8;
 292   2              if(stepper_y.current_pos > 0)
 293   2                  stepper_y.current_pos--;
 294   2          }
 295   1      
 296   1          // 输出相位到引脚 (P3.2,P3.3,P3.6,P3.7)
 297   1          phase = stepper_phase_table[stepper_y.step_phase];
 298   1          P3 = (P3 & 0x33) | ((phase & 0x03) << 2) | ((phase & 0x0C) << 4);
 299   1      
 300   1          // 减少剩余步数
 301   1          stepper_y.step_count--;
C51 COMPILER V9.01   STEPPER                                                               07/20/2025 12:44:14 PAGE 6   

 302   1      }
 303          
 304          /*******************************************************************************
 305           * 函数: Stepper_IsMoving
 306           * 功能: 检查电机是否在运动
 307           * 参数: axis - 轴选择
 308           * 返回: 1-运动中, 0-停止
 309           ******************************************************************************/
 310          uint8_t Stepper_IsMoving(uint8_t axis)
 311          {
 312   1          if(axis == STEPPER_X_AXIS)
 313   1              return (stepper_x.state == STEPPER_STATE_MOVE);
 314   1          else
 315   1              return (stepper_y.state == STEPPER_STATE_MOVE);
 316   1      }
 317          
 318          /*******************************************************************************
 319           * 函数: Stepper_GetPosition
 320           * 功能: 获取当前位置
 321           * 参数: axis - 轴选择
 322           * 返回: 当前位置
 323           ******************************************************************************/
 324          uint16_t Stepper_GetPosition(uint8_t axis)
 325          {
 326   1          if(axis == STEPPER_X_AXIS)
 327   1              return stepper_x.current_pos;
 328   1          else
 329   1              return stepper_y.current_pos;
 330   1      }
 331          
 332          /*******************************************************************************
 333           * 函数: Stepper_SetPosition
 334           * 功能: 设置当前位置
 335           * 参数: axis - 轴选择, position - 位置值
 336           * 返回: 无
 337           ******************************************************************************/
 338          void Stepper_SetPosition(uint8_t axis, uint16_t position)
 339          {
 340   1          if(axis == STEPPER_X_AXIS)
 341   1              stepper_x.current_pos = position;
 342   1          else
 343   1              stepper_y.current_pos = position;
 344   1      }
 345          
 346          /*******************************************************************************
 347           * 函数: Stepper_Home
 348           * 功能: 回零操作
 349           * 参数: axis - 轴选择
 350           * 返回: 无
 351           ******************************************************************************/
 352          void Stepper_Home(uint8_t axis)
 353          {
 354   1          // 简单的回零实现 - 移动到0位置
 355   1          Stepper_MoveTo(axis, 0);
 356   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    693    ----
   CONSTANT SIZE    =      8    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
C51 COMPILER V9.01   STEPPER                                                               07/20/2025 12:44:14 PAGE 7   

   DATA SIZE        =     27      11
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  1 WARNING(S),  0 ERROR(S)
