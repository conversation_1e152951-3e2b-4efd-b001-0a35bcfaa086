C51 COMPILER V9.01   SYSTEM                                                                07/20/2025 12:44:14 PAGE 1   


C51 COMPILER V9.01, COMPILATION OF MODULE SYSTEM
OBJECT MODULE PLACED IN .\Objects\system.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\Source\system.c BROWSE INCDIR(..\Source) DEBUG OBJECTEXTEND PRINT(.\List
                    -ings\system.lst) OBJECT(.\Objects\system.obj)

line level    source

*** WARNING C500 IN LINE 1 OF ..\SOURCE\SYSTEM.C: LICENSE ERROR (R208: RENEW LICENSE ID CODE (LIC))

   1          /*******************************************************************************
   2           * 系统管理模块实现
   3           * 功能: 系统初始化、状态管理、安全保护
   4           ******************************************************************************/
   5          
   6          #include "system.h"
   7          #include "stepper.h"
   8          #include "laser.h"
   9          
  10          /*******************************************************************************
  11           * 外部变量声明
  12           ******************************************************************************/
  13          extern volatile uint8_t system_state;
  14          
  15          /*******************************************************************************
  16           * 函数: System_Init
  17           * 功能: 系统初始化
  18           * 参数: 无
  19           * 返回: 无
  20           ******************************************************************************/
  21          void System_Init(void)
  22          {
  23   1          // 设置系统时钟
  24   1          // STC15W201S默认使用内部RC振荡器，可以配置为12MHz
  25   1          
  26   1          // 初始化I/O端口
  27   1          P0 = 0xFF;  // 设置P0口为高电平
  28   1          P1 = 0xFF;  // 设置P1口为高电平
  29   1          P2 = 0xFF;  // 设置P2口为高电平
  30   1          P3 = 0xFF;  // 设置P3口为高电平
  31   1          
  32   1          // 设置系统状态
  33   1          system_state = SYS_IDLE;
  34   1          
  35   1          // 禁用看门狗 (如果需要可以启用)
  36   1          // WDT_CONTR = 0x00;
  37   1      }
  38          
  39          /*******************************************************************************
  40           * 函数: System_Reset
  41           * 功能: 系统复位
  42           * 参数: 无
  43           * 返回: 无
  44           ******************************************************************************/
  45          void System_Reset(void)
  46          {
  47   1          // 停止所有运动
  48   1          Stepper_Stop(STEPPER_X_AXIS);
  49   1          Stepper_Stop(STEPPER_Y_AXIS);
  50   1          
  51   1          // 关闭激光
  52   1          Laser_Off();
  53   1          
C51 COMPILER V9.01   SYSTEM                                                                07/20/2025 12:44:14 PAGE 2   

  54   1          // 重置位置到原点
  55   1          Stepper_SetPosition(STEPPER_X_AXIS, 0);
  56   1          Stepper_SetPosition(STEPPER_Y_AXIS, 0);
  57   1          
  58   1          // 设置系统状态为空闲
  59   1          system_state = SYS_IDLE;
  60   1      }
  61          
  62          /*******************************************************************************
  63           * 函数: System_Pause
  64           * 功能: 系统暂停
  65           * 参数: 无
  66           * 返回: 无
  67           ******************************************************************************/
  68          void System_Pause(void)
  69          {
  70   1          if(system_state == SYS_RUNNING)
  71   1          {
  72   2              // 停止运动但保持位置
  73   2              Stepper_Stop(STEPPER_X_AXIS);
  74   2              Stepper_Stop(STEPPER_Y_AXIS);
  75   2              
  76   2              // 关闭激光
  77   2              Laser_Off();
  78   2              
  79   2              // 设置系统状态为暂停
  80   2              system_state = SYS_PAUSE;
  81   2          }
  82   1      }
  83          
  84          /*******************************************************************************
  85           * 函数: System_Continue
  86           * 功能: 系统恢复
  87           * 参数: 无
  88           * 返回: 无
  89           ******************************************************************************/
  90          void System_Continue(void)
  91          {
  92   1          if(system_state == SYS_PAUSE)
  93   1          {
  94   2              // 恢复系统状态为运行
  95   2              system_state = SYS_RUNNING;
  96   2              
  97   2              // 注意：这里不自动恢复运动和激光，需要上位机重新发送命令
  98   2          }
  99   1      }
 100          
 101          /*******************************************************************************
 102           * 函数: System_Stop
 103           * 功能: 系统停止
 104           * 参数: 无
 105           * 返回: 无
 106           ******************************************************************************/
 107          void System_Stop(void)
 108          {
 109   1          // 立即停止所有运动
 110   1          Stepper_Stop(STEPPER_X_AXIS);
 111   1          Stepper_Stop(STEPPER_Y_AXIS);
 112   1          
 113   1          // 立即关闭激光
 114   1          Laser_Off();
 115   1          
C51 COMPILER V9.01   SYSTEM                                                                07/20/2025 12:44:14 PAGE 3   

 116   1          // 设置系统状态为空闲
 117   1          system_state = SYS_IDLE;
 118   1      }
 119          
 120          /*******************************************************************************
 121           * 函数: System_Monitor
 122           * 功能: 系统监控
 123           * 参数: 无
 124           * 返回: 无
 125           ******************************************************************************/
 126          void System_Monitor(void)
 127          {
 128   1          // 检查系统状态
 129   1          if(system_state == SYS_RUNNING)
 130   1          {
 131   2              // 检查是否有运动在进行
 132   2              if(!Stepper_IsMoving(STEPPER_X_AXIS) && !Stepper_IsMoving(STEPPER_Y_AXIS))
 133   2              {
 134   3                  // 如果没有运动，可以考虑将状态改为空闲
 135   3                  // system_state = SYS_IDLE;
 136   3              }
 137   2          }
 138   1          
 139   1          // 安全检查 - 这里可以添加各种安全检查
 140   1          // 例如：温度检查、限位开关检查等
 141   1          
 142   1          // 示例：简单的安全检查
 143   1          // if(某种错误条件)
 144   1          // {
 145   1          //     System_EmergencyStop();
 146   1          // }
 147   1      }
 148          
 149          /*******************************************************************************
 150           * 函数: System_WatchdogFeed
 151           * 功能: 看门狗喂狗
 152           * 参数: 无
 153           * 返回: 无
 154           ******************************************************************************/
 155          void System_WatchdogFeed(void)
 156          {
 157   1          // 如果启用了看门狗，在这里喂狗
 158   1          // WDT_CONTR |= 0x10;  // 清除看门狗
 159   1      }
 160          
 161          /*******************************************************************************
 162           * 函数: System_EmergencyStop
 163           * 功能: 紧急停止
 164           * 参数: 无
 165           * 返回: 无
 166           ******************************************************************************/
 167          void System_EmergencyStop(void)
 168          {
 169   1          // 立即停止所有运动
 170   1          Stepper_Stop(STEPPER_X_AXIS);
 171   1          Stepper_Stop(STEPPER_Y_AXIS);
 172   1          
 173   1          // 立即关闭激光
 174   1          Laser_Off();
 175   1          
 176   1          // 设置系统状态为错误
 177   1          system_state = SYS_ERROR;
C51 COMPILER V9.01   SYSTEM                                                                07/20/2025 12:44:14 PAGE 4   

 178   1          
 179   1          // 可以添加报警信号
 180   1          // 例如：蜂鸣器、LED指示等
 181   1      }
 182          
 183          /*******************************************************************************
 184           * 函数: System_GetState
 185           * 功能: 获取系统状态
 186           * 参数: 无
 187           * 返回: 系统状态
 188           ******************************************************************************/
 189          uint8_t System_GetState(void)
 190          {
 191   1          return system_state;
 192   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    136    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  1 WARNING(S),  0 ERROR(S)
