/*******************************************************************************
 * 步进电机控制模块实现
 * 功能: 控制X轴和Y轴步进电机运动
 ******************************************************************************/

#include "stepper.h"

/*******************************************************************************
 * 全局变量定义
 ******************************************************************************/
StepperMotor_t stepper_x;
StepperMotor_t stepper_y;
volatile uint8_t stepper_speed = 50;  // 默认速度50%

/*******************************************************************************
 * 步进电机相位表 (四相八拍)
 ******************************************************************************/
code uint8_t stepper_phase_table[8] = {
    0x01,  // 0001 - A相
    0x03,  // 0011 - A+B相
    0x02,  // 0010 - B相
    0x06,  // 0110 - B+C相
    0x04,  // 0100 - C相
    0x0C,  // 1100 - C+D相
    0x08,  // 1000 - D相
    0x09   // 1001 - D+A相
};

/*******************************************************************************
 * 函数: Stepper_Init
 * 功能: 步进电机初始化
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Stepper_Init(void)
{
    // 初始化X轴步进电机
    stepper_x.target_pos = 0;
    stepper_x.current_pos = 0;
    stepper_x.direction = STEPPER_DIR_CW;
    stepper_x.state = STEPPER_STATE_IDLE;
    stepper_x.step_count = 0;
    stepper_x.step_phase = 0;
    stepper_x.step_delay = 100;  // 默认延时
    stepper_x.step_timer = 0;
    
    // 初始化Y轴步进电机
    stepper_y.target_pos = 0;
    stepper_y.current_pos = 0;
    stepper_y.direction = STEPPER_DIR_CW;
    stepper_y.state = STEPPER_STATE_IDLE;
    stepper_y.step_count = 0;
    stepper_y.step_phase = 0;
    stepper_y.step_delay = 100;  // 默认延时
    stepper_y.step_timer = 0;
    
    // 设置步进电机引脚为输出
    P1 |= 0x0F;  // X轴引脚设为高电平
    P3 |= 0xCC;  // Y轴引脚设为高电平 (P3.2,P3.3,P3.6,P3.7)
    
    // 初始化定时器0用于步进电机脉冲生成
    TMOD &= 0xF0;   // 清除定时器0模式位
    TMOD |= 0x01;   // 定时器0工作在模式1（16位定时器）
    TH0 = 0xFC;     // 设置初值 (1ms @ 12MHz)
    TL0 = 0x18;
    TR0 = 1;        // 启动定时器0
    ET0 = 1;        // 使能定时器0中断
}

/*******************************************************************************
 * 函数: Stepper_MoveTo
 * 功能: 移动到指定位置
 * 参数: axis - 轴选择, position - 目标位置
 * 返回: 无
 ******************************************************************************/
void Stepper_MoveTo(uint8_t axis, uint16_t position)
{
    StepperMotor_t *motor;
    
    // 选择电机
    if(axis == STEPPER_X_AXIS)
        motor = &stepper_x;
    else
        motor = &stepper_y;
    
    // 计算运动参数
    motor->target_pos = position;
    
    if(position > motor->current_pos)
    {
        motor->direction = STEPPER_DIR_CW;
        motor->step_count = position - motor->current_pos;
    }
    else if(position < motor->current_pos)
    {
        motor->direction = STEPPER_DIR_CCW;
        motor->step_count = motor->current_pos - position;
    }
    else
    {
        motor->step_count = 0;
        return;  // 已在目标位置
    }
    
    // 启动运动
    motor->state = STEPPER_STATE_MOVE;
    motor->step_timer = 0;
}

/*******************************************************************************
 * 函数: Stepper_MoveRelative
 * 功能: 相对移动
 * 参数: axis - 轴选择, steps - 移动步数(正数为正向，负数为反向)
 * 返回: 无
 ******************************************************************************/
void Stepper_MoveRelative(uint8_t axis, int16_t steps)
{
    StepperMotor_t *motor;
    uint16_t target_pos;
    
    // 选择电机
    if(axis == STEPPER_X_AXIS)
        motor = &stepper_x;
    else
        motor = &stepper_y;
    
    // 计算目标位置
    if(steps >= 0)
    {
        target_pos = motor->current_pos + steps;
    }
    else
    {
        if(motor->current_pos >= (-steps))
            target_pos = motor->current_pos + steps;
        else
            target_pos = 0;
    }
    
    // 执行移动
    Stepper_MoveTo(axis, target_pos);
}

/*******************************************************************************
 * 函数: Stepper_SetSpeed
 * 功能: 设置运动速度
 * 参数: speed - 速度百分比 (1-100)
 * 返回: 无
 ******************************************************************************/
void Stepper_SetSpeed(uint8_t speed)
{
    uint16_t delay;
    
    // 限制速度范围
    if(speed < MIN_SPEED) speed = MIN_SPEED;
    if(speed > MAX_SPEED) speed = MAX_SPEED;
    
    stepper_speed = speed;
    
    // 计算步进延时 (速度越高，延时越小)
    delay = 200 - speed;  // 100-200ms范围
    
    stepper_x.step_delay = delay;
    stepper_y.step_delay = delay;
}

/*******************************************************************************
 * 函数: Stepper_Stop
 * 功能: 停止电机运动
 * 参数: axis - 轴选择
 * 返回: 无
 ******************************************************************************/
void Stepper_Stop(uint8_t axis)
{
    if(axis == STEPPER_X_AXIS)
    {
        stepper_x.state = STEPPER_STATE_IDLE;
        stepper_x.step_count = 0;
    }
    else
    {
        stepper_y.state = STEPPER_STATE_IDLE;
        stepper_y.step_count = 0;
    }
}

/*******************************************************************************
 * 函数: Stepper_Process
 * 功能: 步进电机处理函数(主循环调用)
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Stepper_Process(void)
{
    // 这里可以添加额外的处理逻辑
    // 主要的步进控制在定时器中断中完成
}

/*******************************************************************************
 * 函数: Stepper_TimerISR
 * 功能: 定时器中断服务程序
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Stepper_TimerISR(void)
{
    // 处理X轴步进电机
    if(stepper_x.state == STEPPER_STATE_MOVE)
    {
        stepper_x.step_timer++;
        if(stepper_x.step_timer >= stepper_x.step_delay)
        {
            stepper_x.step_timer = 0;
            Stepper_StepX();
        }
    }

    // 处理Y轴步进电机
    if(stepper_y.state == STEPPER_STATE_MOVE)
    {
        stepper_y.step_timer++;
        if(stepper_y.step_timer >= stepper_y.step_delay)
        {
            stepper_y.step_timer = 0;
            Stepper_StepY();
        }
    }
}

/*******************************************************************************
 * 函数: Stepper_StepX
 * 功能: X轴步进一步
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Stepper_StepX(void)
{
    uint8_t phase;

    if(stepper_x.step_count == 0)
    {
        stepper_x.state = STEPPER_STATE_IDLE;
        return;
    }

    // 更新相位
    if(stepper_x.direction == STEPPER_DIR_CW)
    {
        stepper_x.step_phase = (stepper_x.step_phase + 1) % 8;
        stepper_x.current_pos++;
    }
    else
    {
        stepper_x.step_phase = (stepper_x.step_phase + 7) % 8;
        if(stepper_x.current_pos > 0)
            stepper_x.current_pos--;
    }

    // 输出相位到引脚
    phase = stepper_phase_table[stepper_x.step_phase];
    P1 = (P1 & 0xF0) | (phase & 0x0F);

    // 减少剩余步数
    stepper_x.step_count--;
}

/*******************************************************************************
 * 函数: Stepper_StepY
 * 功能: Y轴步进一步
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void Stepper_StepY(void)
{
    uint8_t phase;

    if(stepper_y.step_count == 0)
    {
        stepper_y.state = STEPPER_STATE_IDLE;
        return;
    }

    // 更新相位
    if(stepper_y.direction == STEPPER_DIR_CW)
    {
        stepper_y.step_phase = (stepper_y.step_phase + 1) % 8;
        stepper_y.current_pos++;
    }
    else
    {
        stepper_y.step_phase = (stepper_y.step_phase + 7) % 8;
        if(stepper_y.current_pos > 0)
            stepper_y.current_pos--;
    }

    // 输出相位到引脚 (P3.2,P3.3,P3.6,P3.7)
    phase = stepper_phase_table[stepper_y.step_phase];
    P3 = (P3 & 0x33) | ((phase & 0x03) << 2) | ((phase & 0x0C) << 4);

    // 减少剩余步数
    stepper_y.step_count--;
}

/*******************************************************************************
 * 函数: Stepper_IsMoving
 * 功能: 检查电机是否在运动
 * 参数: axis - 轴选择
 * 返回: 1-运动中, 0-停止
 ******************************************************************************/
uint8_t Stepper_IsMoving(uint8_t axis)
{
    if(axis == STEPPER_X_AXIS)
        return (stepper_x.state == STEPPER_STATE_MOVE);
    else
        return (stepper_y.state == STEPPER_STATE_MOVE);
}

/*******************************************************************************
 * 函数: Stepper_GetPosition
 * 功能: 获取当前位置
 * 参数: axis - 轴选择
 * 返回: 当前位置
 ******************************************************************************/
uint16_t Stepper_GetPosition(uint8_t axis)
{
    if(axis == STEPPER_X_AXIS)
        return stepper_x.current_pos;
    else
        return stepper_y.current_pos;
}

/*******************************************************************************
 * 函数: Stepper_SetPosition
 * 功能: 设置当前位置
 * 参数: axis - 轴选择, position - 位置值
 * 返回: 无
 ******************************************************************************/
void Stepper_SetPosition(uint8_t axis, uint16_t position)
{
    if(axis == STEPPER_X_AXIS)
        stepper_x.current_pos = position;
    else
        stepper_y.current_pos = position;
}

/*******************************************************************************
 * 函数: Stepper_Home
 * 功能: 回零操作
 * 参数: axis - 轴选择
 * 返回: 无
 ******************************************************************************/
void Stepper_Home(uint8_t axis)
{
    // 简单的回零实现 - 移动到0位置
    Stepper_MoveTo(axis, 0);
}
