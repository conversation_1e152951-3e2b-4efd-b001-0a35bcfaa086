@echo off
echo ========================================
echo 激光雕刻机控制系统 - SDCC编译脚本
echo ========================================

REM 检查SDCC是否安装
where sdcc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到SDCC编译器
    echo 请从以下地址下载安装SDCC:
    echo http://sdcc.sourceforge.net/
    echo.
    echo 安装后请将SDCC的bin目录添加到系统PATH环境变量
    pause
    exit /b 1
)

echo 检测到SDCC编译器，开始编译...
echo.

REM 复制源文件
echo 复制源文件...
copy "..\MCU_Program\Source\*.c" . >nul 2>&1
copy "..\MCU_Program\Source\*.h" . >nul 2>&1

REM 编译项目
echo 编译中...
make clean
make

if exist LaserEngraver.hex (
    echo.
    echo ========================================
    echo 编译成功！
    echo ========================================
    echo HEX文件: LaserEngraver.hex
    echo 文件大小:
    dir LaserEngraver.hex | find "LaserEngraver.hex"
    echo.
    echo 可以使用STC-ISP或其他工具下载到单片机
) else (
    echo.
    echo ========================================
    echo 编译失败！
    echo ========================================
    echo 请检查错误信息并修复后重试
)

echo.
pause
