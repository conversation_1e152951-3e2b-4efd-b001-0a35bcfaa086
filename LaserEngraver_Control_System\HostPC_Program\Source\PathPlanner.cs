/*******************************************************************************
 * 路径规划器类
 * 功能: 优化雕刻路径，提高效率
 ******************************************************************************/

using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;

namespace LaserEngraverControl
{
    /// <summary>
    /// 路径规划器类
    /// </summary>
    public class PathPlanner
    {
        #region 事件定义
        public event EventHandler<string> StatusChanged;
        public event EventHandler<int> ProgressChanged;
        #endregion

        #region 公共方法
        /// <summary>
        /// 优化路径
        /// </summary>
        /// <param name="originalPath">原始路径</param>
        /// <param name="optimizeOrder">是否优化顺序</param>
        /// <param name="minimizeTravel">是否最小化空行程</param>
        /// <returns>优化后的路径</returns>
        public List<PathPoint> OptimizePath(List<PathPoint> originalPath, bool optimizeOrder = true, bool minimizeTravel = true)
        {
            if (originalPath == null || originalPath.Count == 0)
                return new List<PathPoint>();

            StatusChanged?.Invoke(this, "开始路径优化...");
            
            List<PathPoint> optimizedPath = new List<PathPoint>(originalPath);

            // 1. 移除重复点
            optimizedPath = RemoveDuplicatePoints(optimizedPath);
            ProgressChanged?.Invoke(this, 25);

            // 2. 优化顺序 (最近邻算法)
            if (optimizeOrder)
            {
                optimizedPath = OptimizePathOrder(optimizedPath);
                ProgressChanged?.Invoke(this, 50);
            }

            // 3. 最小化空行程
            if (minimizeTravel)
            {
                optimizedPath = MinimizeTravelDistance(optimizedPath);
                ProgressChanged?.Invoke(this, 75);
            }

            // 4. 平滑路径
            optimizedPath = SmoothPath(optimizedPath);
            ProgressChanged?.Invoke(this, 100);

            StatusChanged?.Invoke(this, $"路径优化完成，从{originalPath.Count}个点优化为{optimizedPath.Count}个点");
            
            return optimizedPath;
        }

        /// <summary>
        /// 移除重复点
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>去重后的路径</returns>
        private List<PathPoint> RemoveDuplicatePoints(List<PathPoint> path)
        {
            List<PathPoint> result = new List<PathPoint>();
            const float tolerance = 0.1f; // 容差

            for (int i = 0; i < path.Count; i++)
            {
                bool isDuplicate = false;
                
                if (result.Count > 0)
                {
                    PathPoint lastPoint = result[result.Count - 1];
                    float distance = CalculateDistance(lastPoint.Position, path[i].Position);
                    
                    if (distance < tolerance && lastPoint.Type == path[i].Type)
                    {
                        isDuplicate = true;
                    }
                }

                if (!isDuplicate)
                {
                    result.Add(path[i]);
                }
            }

            return result;
        }

        /// <summary>
        /// 优化路径顺序 (简化的最近邻算法)
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>优化顺序后的路径</returns>
        private List<PathPoint> OptimizePathOrder(List<PathPoint> path)
        {
            // 将路径分割为独立的图形
            List<List<PathPoint>> shapes = SeparateShapes(path);
            
            if (shapes.Count <= 1)
                return path;

            // 使用最近邻算法重新排序图形
            List<PathPoint> result = new List<PathPoint>();
            List<List<PathPoint>> remainingShapes = new List<List<PathPoint>>(shapes);
            PointF currentPosition = new PointF(0, 0);

            while (remainingShapes.Count > 0)
            {
                // 找到最近的图形
                int nearestIndex = 0;
                float minDistance = float.MaxValue;

                for (int i = 0; i < remainingShapes.Count; i++)
                {
                    PointF shapeStart = GetShapeStartPoint(remainingShapes[i]);
                    float distance = CalculateDistance(currentPosition, shapeStart);
                    
                    if (distance < minDistance)
                    {
                        minDistance = distance;
                        nearestIndex = i;
                    }
                }

                // 添加最近的图形到结果中
                List<PathPoint> nearestShape = remainingShapes[nearestIndex];
                result.AddRange(nearestShape);
                currentPosition = GetShapeEndPoint(nearestShape);
                remainingShapes.RemoveAt(nearestIndex);
            }

            return result;
        }

        /// <summary>
        /// 将路径分割为独立的图形
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>图形列表</returns>
        private List<List<PathPoint>> SeparateShapes(List<PathPoint> path)
        {
            List<List<PathPoint>> shapes = new List<List<PathPoint>>();
            List<PathPoint> currentShape = new List<PathPoint>();

            foreach (PathPoint point in path)
            {
                if (point.Type == PathPointType.MoveTo && currentShape.Count > 0)
                {
                    // 开始新图形
                    shapes.Add(new List<PathPoint>(currentShape));
                    currentShape.Clear();
                }
                
                currentShape.Add(point);
            }

            if (currentShape.Count > 0)
            {
                shapes.Add(currentShape);
            }

            return shapes;
        }

        /// <summary>
        /// 获取图形的起始点
        /// </summary>
        /// <param name="shape">图形路径</param>
        /// <returns>起始点</returns>
        private PointF GetShapeStartPoint(List<PathPoint> shape)
        {
            return shape.Count > 0 ? shape[0].Position : new PointF(0, 0);
        }

        /// <summary>
        /// 获取图形的结束点
        /// </summary>
        /// <param name="shape">图形路径</param>
        /// <returns>结束点</returns>
        private PointF GetShapeEndPoint(List<PathPoint> shape)
        {
            return shape.Count > 0 ? shape[shape.Count - 1].Position : new PointF(0, 0);
        }

        /// <summary>
        /// 最小化空行程距离
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>优化后的路径</returns>
        private List<PathPoint> MinimizeTravelDistance(List<PathPoint> path)
        {
            List<PathPoint> result = new List<PathPoint>();

            for (int i = 0; i < path.Count; i++)
            {
                PathPoint currentPoint = path[i];
                
                // 如果是移动命令且距离很短，可以考虑合并
                if (currentPoint.Type == PathPointType.MoveTo && i > 0)
                {
                    PathPoint previousPoint = path[i - 1];
                    float distance = CalculateDistance(previousPoint.Position, currentPoint.Position);
                    
                    // 如果移动距离很短，可以考虑用直线代替
                    if (distance < 2.0f && i < path.Count - 1)
                    {
                        PathPoint nextPoint = path[i + 1];
                        if (nextPoint.Type == PathPointType.LineTo)
                        {
                            // 跳过这个MoveTo，直接连线
                            continue;
                        }
                    }
                }

                result.Add(currentPoint);
            }

            return result;
        }

        /// <summary>
        /// 平滑路径
        /// </summary>
        /// <param name="path">原始路径</param>
        /// <returns>平滑后的路径</returns>
        private List<PathPoint> SmoothPath(List<PathPoint> path)
        {
            // 简单的平滑算法：移除角度变化很小的中间点
            List<PathPoint> result = new List<PathPoint>();
            const float angleThreshold = 5.0f; // 角度阈值（度）

            for (int i = 0; i < path.Count; i++)
            {
                bool shouldKeep = true;

                if (i > 0 && i < path.Count - 1)
                {
                    PathPoint prev = path[i - 1];
                    PathPoint curr = path[i];
                    PathPoint next = path[i + 1];

                    // 只对LineTo类型的点进行平滑处理
                    if (curr.Type == PathPointType.LineTo && 
                        prev.Type == PathPointType.LineTo && 
                        next.Type == PathPointType.LineTo)
                    {
                        float angle = CalculateAngle(prev.Position, curr.Position, next.Position);
                        if (Math.Abs(angle) < angleThreshold)
                        {
                            shouldKeep = false; // 角度变化很小，可以移除这个点
                        }
                    }
                }

                if (shouldKeep)
                {
                    result.Add(path[i]);
                }
            }

            return result;
        }

        /// <summary>
        /// 计算两点之间的距离
        /// </summary>
        /// <param name="point1">点1</param>
        /// <param name="point2">点2</param>
        /// <returns>距离</returns>
        private float CalculateDistance(PointF point1, PointF point2)
        {
            float dx = point2.X - point1.X;
            float dy = point2.Y - point1.Y;
            return (float)Math.Sqrt(dx * dx + dy * dy);
        }

        /// <summary>
        /// 计算三点形成的角度
        /// </summary>
        /// <param name="point1">点1</param>
        /// <param name="point2">点2（顶点）</param>
        /// <param name="point3">点3</param>
        /// <returns>角度（度）</returns>
        private float CalculateAngle(PointF point1, PointF point2, PointF point3)
        {
            float dx1 = point1.X - point2.X;
            float dy1 = point1.Y - point2.Y;
            float dx2 = point3.X - point2.X;
            float dy2 = point3.Y - point2.Y;

            double angle1 = Math.Atan2(dy1, dx1);
            double angle2 = Math.Atan2(dy2, dx2);
            double angleDiff = angle2 - angle1;

            // 将角度转换为度并标准化到[-180, 180]范围
            angleDiff = angleDiff * 180.0 / Math.PI;
            while (angleDiff > 180) angleDiff -= 360;
            while (angleDiff < -180) angleDiff += 360;

            return (float)angleDiff;
        }

        /// <summary>
        /// 计算路径总长度
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>总长度</returns>
        public float CalculatePathLength(List<PathPoint> path)
        {
            float totalLength = 0;
            
            for (int i = 1; i < path.Count; i++)
            {
                totalLength += CalculateDistance(path[i - 1].Position, path[i].Position);
            }

            return totalLength;
        }

        /// <summary>
        /// 估算雕刻时间
        /// </summary>
        /// <param name="path">路径</param>
        /// <param name="averageSpeed">平均速度 (mm/min)</param>
        /// <returns>估算时间（分钟）</returns>
        public float EstimateEngravingTime(List<PathPoint> path, float averageSpeed = 1000)
        {
            float totalLength = CalculatePathLength(path);
            return totalLength / averageSpeed;
        }
        #endregion
    }
}
