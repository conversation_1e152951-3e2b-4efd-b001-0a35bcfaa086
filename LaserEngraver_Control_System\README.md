# 激光雕刻机控制系统

## 项目概述
本项目是一个完整的激光雕刻机控制系统，包含单片机程序和上位机程序两部分。

## 系统架构
- **单片机程序**: 基于STC15W201S芯片，负责底层硬件控制
- **上位机程序**: 基于C# WinForms，提供用户界面和高级功能
- **通信协议**: 自定义串口通信协议，实现上位机与单片机的数据交换

## 硬件配置

### 目标芯片: STC15W201S
- 工作电压: 3.3V/5V
- 工作频率: 最高35MHz
- Flash: 13KB
- RAM: 1280字节

### 引脚定义
| 功能 | 引脚 | 端口 | 说明 |
|------|------|------|------|
| 串口接收 | 9 | P3.0 (RXD) | 接收上位机数据 |
| 串口发送 | 10 | P3.1 (TXD) | 发送数据到上位机 |
| X轴步进电机 | 15,16,1,2 | P1.0-P1.3 | X轴运动控制 |
| Y轴步进电机 | 11,12,13,14 | P3.2,P3.3,P3.6,P3.7 | Y轴运动控制 |

## 目录结构
```
LaserEngraver_Control_System/
├── MCU_Program/                 # 单片机程序
│   ├── Source/                  # 源代码文件
│   ├── Keil_Project/           # Keil工程文件
│   └── Libraries/              # 库文件
├── HostPC_Program/             # 上位机程序
│   ├── Source/                 # C#源代码
│   ├── Resources/              # 资源文件
│   ├── bin/                    # 编译输出
│   └── obj/                    # 编译临时文件
├── Communication_Protocol/      # 通信协议文档
└── Documentation/              # 项目文档
```

## 功能特性

### 单片机功能
- [x] 串口通信 (波特率: 9600bps)
- [x] 双轴步进电机控制
- [x] 激光功率PWM控制
- [x] 运动轨迹插补
- [x] 安全保护机制
- [x] 实时状态反馈

### 上位机功能
- [x] 图形化用户界面
- [x] 文件导入 (DXF, SVG, G-code)
- [x] 参数设置和配置
- [x] 实时监控和状态显示
- [x] 雕刻路径规划和优化
- [x] 串口通信管理
- [x] 进度控制和暂停/恢复

## 开发环境
- **单片机**: Keil uVision 5 (路径: C:\Keil_v5\UV4)
- **上位机**: Visual Studio 2019/2022
- **通信**: 串口 (RS232/USB转串口)

## 快速开始
1. 打开Keil工程文件编译单片机程序
2. 使用Visual Studio打开上位机工程
3. 连接硬件并配置串口参数
4. 运行上位机程序开始使用

## 版本信息
- 版本: v1.0.0
- 开发日期: 2025年7月
- 开发者: AI Assistant

## 许可证
本项目仅供学习和研究使用。
