"""
文件处理服务
"""

import pandas as pd
import os
import json
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class FileHandler:
    """文件处理器"""
    
    def __init__(self):
        self.supported_formats = ['xlsx', 'xls', 'csv']
    
    def validate_file(self, file_path, file_extension):
        """验证文件格式和内容"""
        try:
            if file_extension not in self.supported_formats:
                return {
                    'valid': False,
                    'message': f'不支持的文件格式: {file_extension}'
                }
            
            # 尝试读取文件
            if file_extension in ['xlsx', 'xls']:
                df = pd.read_excel(file_path, sheet_name=0, nrows=5)
            elif file_extension == 'csv':
                df = pd.read_csv(file_path, nrows=5)
            
            if df.empty:
                return {
                    'valid': False,
                    'message': '文件为空或无法读取'
                }
            
            # 检查是否是电池测试数据格式
            battery_columns = ['批次号', '通道号', '电池编码', '电压', 'Rs', 'Rct']
            has_battery_data = any(col in str(df.columns).lower() for col in ['批次', '电池', '电压', '阻抗'])
            
            preview_data = {
                'rows': len(df),
                'columns': len(df.columns),
                'column_names': df.columns.tolist(),
                'sample_data': df.head(3).to_dict('records'),
                'is_battery_data': has_battery_data
            }
            
            return {
                'valid': True,
                'message': '文件验证成功',
                'preview_data': preview_data
            }
            
        except Exception as e:
            logger.error(f"文件验证错误: {str(e)}")
            return {
                'valid': False,
                'message': f'文件验证失败: {str(e)}'
            }
    
    def read_excel_file(self, file_path):
        """读取Excel文件"""
        try:
            # 读取所有工作表
            excel_data = pd.read_excel(file_path, sheet_name=None)
            
            result = {
                'sheets': {},
                'sheet_names': list(excel_data.keys())
            }
            
            for sheet_name, df in excel_data.items():
                result['sheets'][sheet_name] = {
                    'data': df,
                    'rows': len(df),
                    'columns': len(df.columns),
                    'column_names': df.columns.tolist()
                }
            
            return result
            
        except Exception as e:
            logger.error(f"读取Excel文件错误: {str(e)}")
            raise Exception(f"无法读取Excel文件: {str(e)}")
    
    def read_csv_file(self, file_path, encoding='utf-8'):
        """读取CSV文件"""
        try:
            # 尝试不同的编码
            encodings = [encoding, 'utf-8', 'gbk', 'gb2312', 'latin1']
            
            for enc in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=enc)
                    return {
                        'data': df,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'column_names': df.columns.tolist(),
                        'encoding': enc
                    }
                except UnicodeDecodeError:
                    continue
            
            raise Exception("无法确定文件编码")
            
        except Exception as e:
            logger.error(f"读取CSV文件错误: {str(e)}")
            raise Exception(f"无法读取CSV文件: {str(e)}")
    
    def parse_battery_data(self, df):
        """解析电池测试数据"""
        try:
            # 标准化列名映射
            column_mapping = {
                '批次号': 'batch_number',
                '通道号': 'channel_number', 
                '电池编码': 'battery_code',
                '测试开始时间': 'test_start_time',
                '测试结束时间': 'test_end_time',
                '测试时长(s)': 'test_duration',
                '电压(V)': 'voltage',
                'Rs(mΩ)': 'rs_impedance',
                'Rct(mΩ)': 'rct_impedance',
                'W阻抗(mΩ)': 'w_impedance',
                'Rs档位': 'rs_range',
                'Rct档位': 'rct_range',
                '电压范围': 'voltage_range',
                'Rs范围': 'rs_range_desc',
                'Rct范围': 'rct_range_desc',
                '离群率(%)': 'outlier_rate',
                '基准ID': 'reference_id',
                '测试结果': 'test_result',
                '失败原因': 'failure_reason',
                '操作员': 'operator',
                '电池类型': 'battery_type',
                '电池规格': 'battery_spec'
            }
            
            # 重命名列
            df_renamed = df.rename(columns=column_mapping)
            
            # 数据类型转换
            if 'test_start_time' in df_renamed.columns:
                df_renamed['test_start_time'] = pd.to_datetime(df_renamed['test_start_time'], errors='coerce')
            
            if 'test_end_time' in df_renamed.columns:
                df_renamed['test_end_time'] = pd.to_datetime(df_renamed['test_end_time'], errors='coerce')
            
            # 数值列转换
            numeric_columns = ['voltage', 'rs_impedance', 'rct_impedance', 'w_impedance', 
                             'test_duration', 'channel_number', 'outlier_rate']
            
            for col in numeric_columns:
                if col in df_renamed.columns:
                    df_renamed[col] = pd.to_numeric(df_renamed[col], errors='coerce')
            
            return df_renamed
            
        except Exception as e:
            logger.error(f"解析电池数据错误: {str(e)}")
            raise Exception(f"无法解析电池数据: {str(e)}")
    
    def export_to_csv(self, df, output_path):
        """导出为CSV格式"""
        try:
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            return output_path
        except Exception as e:
            logger.error(f"导出CSV错误: {str(e)}")
            raise Exception(f"无法导出CSV文件: {str(e)}")
    
    def export_to_excel(self, data_dict, output_path):
        """导出为Excel格式（支持多个工作表）"""
        try:
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                for sheet_name, df in data_dict.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            return output_path
        except Exception as e:
            logger.error(f"导出Excel错误: {str(e)}")
            raise Exception(f"无法导出Excel文件: {str(e)}")
    
    def get_file_info(self, file_path):
        """获取文件基本信息"""
        try:
            stat = os.stat(file_path)
            return {
                'size': stat.st_size,
                'created_time': datetime.fromtimestamp(stat.st_ctime),
                'modified_time': datetime.fromtimestamp(stat.st_mtime),
                'extension': os.path.splitext(file_path)[1].lower().lstrip('.')
            }
        except Exception as e:
            logger.error(f"获取文件信息错误: {str(e)}")
            return None
