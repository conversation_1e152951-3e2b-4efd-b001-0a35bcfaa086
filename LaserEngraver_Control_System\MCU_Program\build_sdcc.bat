@echo off
REM ============================================================================
REM SDCC 编译脚本 - 激光雕刻机控制系统
REM ============================================================================

echo 开始编译激光雕刻机控制系统...
echo.

REM 设置编译参数
set MCU_MODEL=--model-large
set XRAM_SIZE=--xram-size 1024
set CODE_SIZE=--code-size 8192
set OPTIMIZATION=-O2
set OUTPUT_DIR=Build
set SOURCE_DIR=Source

REM 创建输出目录
if not exist %OUTPUT_DIR% mkdir %OUTPUT_DIR%

REM 编译选项
set COMPILE_OPTIONS=%MCU_MODEL% %XRAM_SIZE% %CODE_SIZE% %OPTIMIZATION% --std-c99

echo 编译参数: %COMPILE_OPTIONS%
echo.

REM 编译所有源文件
echo 编译 main.c...
sdcc %COMPILE_OPTIONS% -c %SOURCE_DIR%\main.c -o %OUTPUT_DIR%\main.rel

echo 编译 uart.c...
sdcc %COMPILE_OPTIONS% -c %SOURCE_DIR%\uart.c -o %OUTPUT_DIR%\uart.rel

echo 编译 stepper.c...
sdcc %COMPILE_OPTIONS% -c %SOURCE_DIR%\stepper.c -o %OUTPUT_DIR%\stepper.rel

echo 编译 laser.c...
sdcc %COMPILE_OPTIONS% -c %SOURCE_DIR%\laser.c -o %OUTPUT_DIR%\laser.rel

echo 编译 protocol.c...
sdcc %COMPILE_OPTIONS% -c %SOURCE_DIR%\protocol.c -o %OUTPUT_DIR%\protocol.rel

echo 编译 system.c...
sdcc %COMPILE_OPTIONS% -c %SOURCE_DIR%\system.c -o %OUTPUT_DIR%\system.rel

REM 链接生成最终文件
echo.
echo 链接生成最终文件...
sdcc %COMPILE_OPTIONS% %OUTPUT_DIR%\main.rel %OUTPUT_DIR%\uart.rel %OUTPUT_DIR%\stepper.rel %OUTPUT_DIR%\laser.rel %OUTPUT_DIR%\protocol.rel %OUTPUT_DIR%\system.rel -o %OUTPUT_DIR%\LaserEngraver.ihx

REM 检查编译结果
if exist %OUTPUT_DIR%\LaserEngraver.ihx (
    echo.
    echo ============================================================================
    echo 编译成功！
    echo 输出文件: %OUTPUT_DIR%\LaserEngraver.ihx
    echo ============================================================================
    echo.
    echo 文件大小信息:
    dir %OUTPUT_DIR%\LaserEngraver.ihx
    echo.
    echo 内存使用情况:
    type %OUTPUT_DIR%\LaserEngraver.mem 2>nul
) else (
    echo.
    echo ============================================================================
    echo 编译失败！请检查错误信息。
    echo ============================================================================
)

echo.
echo 编译完成。按任意键退出...
pause >nul
