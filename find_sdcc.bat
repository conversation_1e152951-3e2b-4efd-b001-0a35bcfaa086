@echo off
echo ========================================
echo SDCC Location Finder
echo ========================================

echo Searching for SDCC installation...
echo.

REM Check common locations
set FOUND=0

echo Checking D:\sdcc...
if exist "D:\sdcc\bin\sdcc.exe" (
    echo [FOUND] D:\sdcc\bin\sdcc.exe
    set FOUND=1
    set SDCC_LOCATION=D:\sdcc
)

echo Checking D:\sdcc-4.2.0...
if exist "D:\sdcc-4.2.0\bin\sdcc.exe" (
    echo [FOUND] D:\sdcc-4.2.0\bin\sdcc.exe
    set FOUND=1
    set SDCC_LOCATION=D:\sdcc-4.2.0
)

echo Checking D:\sdcc-4.3.0...
if exist "D:\sdcc-4.3.0\bin\sdcc.exe" (
    echo [FOUND] D:\sdcc-4.3.0\bin\sdcc.exe
    set FOUND=1
    set SDCC_LOCATION=D:\sdcc-4.3.0
)

echo Checking D:\sdcc-4.4.0...
if exist "D:\sdcc-4.4.0\bin\sdcc.exe" (
    echo [FOUND] D:\sdcc-4.4.0\bin\sdcc.exe
    set FOUND=1
    set SDCC_LOCATION=D:\sdcc-4.4.0
)

echo Checking C:\sdcc...
if exist "C:\sdcc\bin\sdcc.exe" (
    echo [FOUND] C:\sdcc\bin\sdcc.exe
    set FOUND=1
    set SDCC_LOCATION=C:\sdcc
)

echo.
if %FOUND%==1 (
    echo ========================================
    echo SDCC Found!
    echo ========================================
    echo Location: %SDCC_LOCATION%
    echo.
    echo Directory contents:
    dir "%SDCC_LOCATION%" /b
    echo.
    echo bin directory contents:
    dir "%SDCC_LOCATION%\bin" /b | findstr /i "sdcc packihx"
    echo.
    echo Testing SDCC...
    "%SDCC_LOCATION%\bin\sdcc.exe" --version
) else (
    echo ========================================
    echo SDCC Not Found!
    echo ========================================
    echo Please check if SDCC is extracted to one of these locations:
    echo - D:\sdcc\
    echo - C:\sdcc\
    echo.
    echo Or show me the actual location where you extracted SDCC
    echo.
    echo Current D:\ directory contents:
    dir D:\ /b | findstr /i sdcc
)

echo.
echo ========================================
pause
