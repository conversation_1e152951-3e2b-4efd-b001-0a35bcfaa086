C51 COMPILER V9.01   PROTOCOL                                                              07/20/2025 12:44:14 PAGE 1   


C51 COMPILER V9.01, COMPILATION OF MODULE PROTOCOL
OBJECT MODULE PLACED IN .\Objects\protocol.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\Source\protocol.c BROWSE INCDIR(..\Source) DEBUG OBJECTEXTEND PRINT(.\Li
                    -stings\protocol.lst) OBJECT(.\Objects\protocol.obj)

line level    source

*** WARNING C500 IN LINE 1 OF ..\SOURCE\PROTOCOL.C: LICENSE ERROR (R208: RENEW LICENSE ID CODE (LIC))

   1          /*******************************************************************************
   2           * 通信协议处理模块实现
   3           * 功能: 处理与上位机的通信协议
   4           ******************************************************************************/
   5          
   6          #include "protocol.h"
*** ERROR C141 IN LINE 64 OF ..\SOURCE\PROTOCOL.H: syntax error near ','
   7          #include "uart.h"
*** ERROR C141 IN LINE 23 OF ..\SOURCE\UART.H: syntax error near ','
   8          #include "stepper.h"
   9          #include "laser.h"
  10          #include "system.h"
  11          #include <stdio.h>
  12          #include <string.h>
  13          
  14          /*******************************************************************************
  15           * 全局变量定义
  16           ******************************************************************************/
  17          volatile uint8_t protocol_state = PROTOCOL_IDLE;
  18          volatile uint8_t protocol_buffer[PROTOCOL_BUFFER_SIZE];
  19          volatile uint8_t protocol_buffer_index = 0;
  20          
  21          /*******************************************************************************
  22           * 函数: Protocol_Init
  23           * 功能: 协议初始化
  24           * 参数: 无
  25           * 返回: 无
  26           ******************************************************************************/
  27          void Protocol_Init(void)
  28          {
  29   1          protocol_state = PROTOCOL_IDLE;
  30   1          protocol_buffer_index = 0;
  31   1      }
  32          
  33          /*******************************************************************************
  34           * 函数: Protocol_ProcessCommand
  35           * 功能: 处理接收到的命令
  36           * 参数: 无
  37           * 返回: 无
  38           ******************************************************************************/
  39          void Protocol_ProcessCommand(void)
  40          {
  41   1          uint8_t byte;
  42   1          
  43   1          // 检查是否有数据可读
  44   1          while(UART_DataAvailable())
  45   1          {
  46   2              byte = UART_ReceiveByte();
  47   2              
  48   2              switch(protocol_state)
  49   2              {
  50   3                  case PROTOCOL_IDLE:
  51   3                      if(byte == STX)
C51 COMPILER V9.01   PROTOCOL                                                              07/20/2025 12:44:14 PAGE 2   

  52   3                      {
  53   4                          protocol_state = PROTOCOL_RECEIVING;
  54   4                          protocol_buffer_index = 0;
  55   4                      }
  56   3                      break;
  57   3                      
  58   3                  case PROTOCOL_RECEIVING:
  59   3                      if(byte == ETX)
  60   3                      {
  61   4                          protocol_state = PROTOCOL_PROCESSING;
  62   4                          Protocol_ParseCommand();
  63   4                          protocol_state = PROTOCOL_IDLE;
  64   4                      }
  65   3                      else if(protocol_buffer_index < PROTOCOL_BUFFER_SIZE - 1)
  66   3                      {
  67   4                          protocol_buffer[protocol_buffer_index++] = byte;
  68   4                      }
  69   3                      else
  70   3                      {
  71   4                          // 缓冲区溢出，重置状态
  72   4                          protocol_state = PROTOCOL_IDLE;
  73   4                          Protocol_SendError(ERR_FORMAT);
  74   4                      }
  75   3                      break;
  76   3              }
  77   2          }
  78   1      }
  79          
  80          /*******************************************************************************
  81           * 函数: Protocol_ParseCommand
  82           * 功能: 解析并执行命令
  83           * 参数: 无
  84           * 返回: 无
  85           ******************************************************************************/
  86          uint8_t Protocol_ParseCommand(void)
  87          {
  88   1          uint8_t cmd;
  89   1          uint16_t x, y;
  90   1          int16_t dx, dy;
  91   1          uint8_t power, speed;
  92   1          char *data_ptr;
  93   1          
  94   1          if(protocol_buffer_index < 1)
  95   1          {
  96   2              Protocol_SendError(ERR_FORMAT);
  97   2              return 0;
  98   2          }
  99   1          
 100   1          cmd = protocol_buffer[0];
 101   1          data_ptr = (char*)&protocol_buffer[1];
 102   1          
 103   1          switch(cmd)
 104   1          {
 105   2              case CMD_MOVE_TO:  // M<X坐标>,<Y坐标>
 106   2                  if(sscanf(data_ptr, "%u,%u", &x, &y) == 2)
 107   2                  {
 108   3                      Stepper_MoveTo(STEPPER_X_AXIS, x);
 109   3                      Stepper_MoveTo(STEPPER_Y_AXIS, y);
 110   3                      Protocol_SendResponse(RESP_OK);
 111   3                  }
 112   2                  else
 113   2                  {
C51 COMPILER V9.01   PROTOCOL                                                              07/20/2025 12:44:14 PAGE 3   

 114   3                      Protocol_SendError(ERR_PARAM);
 115   3                  }
 116   2                  break;
 117   2                  
 118   2              case CMD_MOVE_REL:  // R<X偏移>,<Y偏移>
 119   2                  if(sscanf(data_ptr, "%d,%d", &dx, &dy) == 2)
 120   2                  {
 121   3                      Stepper_MoveRelative(STEPPER_X_AXIS, dx);
 122   3                      Stepper_MoveRelative(STEPPER_Y_AXIS, dy);
 123   3                      Protocol_SendResponse(RESP_OK);
 124   3                  }
 125   2                  else
 126   2                  {
 127   3                      Protocol_SendError(ERR_PARAM);
 128   3                  }
 129   2                  break;
 130   2                  
 131   2              case CMD_SET_SPEED:  // V<速度值>
 132   2                  if(sscanf(data_ptr, "%u", &speed) == 1)
 133   2                  {
 134   3                      if(speed >= MIN_SPEED && speed <= MAX_SPEED)
 135   3                      {
 136   4                          Stepper_SetSpeed(speed);
 137   4                          Protocol_SendResponse(RESP_OK);
 138   4                      }
 139   3                      else
 140   3                      {
 141   4                          Protocol_SendError(ERR_PARAM);
 142   4                      }
 143   3                  }
 144   2                  else
 145   2                  {
 146   3                      Protocol_SendError(ERR_PARAM);
 147   3                  }
 148   2                  break;
 149   2                  
 150   2              case CMD_LASER_ON:  // L<功率值>
 151   2                  if(sscanf(data_ptr, "%u", &power) == 1)
 152   2                  {
 153   3                      if(power <= LASER_MAX_POWER)
 154   3                      {
 155   4                          Laser_SetPower(power);
 156   4                          Laser_On();
 157   4                          Protocol_SendResponse(RESP_OK);
 158   4                      }
 159   3                      else
 160   3                      {
 161   4                          Protocol_SendError(ERR_PARAM);
 162   4                      }
 163   3                  }
 164   2                  else
 165   2                  {
 166   3                      Protocol_SendError(ERR_PARAM);
 167   3                  }
 168   2                  break;
 169   2                  
 170   2              case CMD_LASER_OFF:  // O
 171   2                  Laser_Off();
 172   2                  Protocol_SendResponse(RESP_OK);
 173   2                  break;
 174   2                  
 175   2              case CMD_RESET:  // Z
C51 COMPILER V9.01   PROTOCOL                                                              07/20/2025 12:44:14 PAGE 4   

 176   2                  System_Reset();
 177   2                  Protocol_SendResponse(RESP_OK);
 178   2                  break;
 179   2                  
 180   2              case CMD_PAUSE:  // P
 181   2                  System_Pause();
 182   2                  Protocol_SendResponse(RESP_OK);
 183   2                  break;
 184   2                  
 185   2              case CMD_CONTINUE:  // C
 186   2                  System_Continue();
 187   2                  Protocol_SendResponse(RESP_OK);
 188   2                  break;
 189   2                  
 190   2              case CMD_STOP:  // S
 191   2                  System_Stop();
 192   2                  Protocol_SendResponse(RESP_OK);
 193   2                  break;
 194   2                  
 195   2              case CMD_QUERY_POS:  // Q
 196   2                  Protocol_SendPosition();
 197   2                  break;
 198   2                  
 199   2              case CMD_QUERY_STA:  // T
 200   2                  Protocol_SendStatus();
 201   2                  break;
 202   2                  
 203   2              default:
 204   2                  Protocol_SendError(ERR_UNKNOWN);
 205   2                  break;
 206   2          }
 207   1          
 208   1          return 1;
 209   1      }
 210          
 211          /*******************************************************************************
 212           * 函数: Protocol_SendResponse
 213           * 功能: 发送响应
 214           * 参数: response - 响应字符串
 215           * 返回: 无
 216           ******************************************************************************/
 217          void Protocol_SendResponse(char *response)
 218          {
 219   1          UART_SendByte(STX);
 220   1          UART_SendString(response);
 221   1          UART_SendByte(ETX);
 222   1          // 这里应该计算并发送校验和，简化处理
 223   1          UART_SendString("00");  // 简化的校验和
 224   1      }
 225          
 226          /*******************************************************************************
 227           * 函数: Protocol_SendError
 228           * 功能: 发送错误响应
 229           * 参数: error_code - 错误码
 230           * 返回: 无
 231           ******************************************************************************/
 232          void Protocol_SendError(char *error_code)
 233          {
 234   1          UART_SendByte(STX);
 235   1          UART_SendString(RESP_ERR);
 236   1          UART_SendString(error_code);
 237   1          UART_SendByte(ETX);
C51 COMPILER V9.01   PROTOCOL                                                              07/20/2025 12:44:14 PAGE 5   

 238   1          UART_SendString("00");  // 简化的校验和
 239   1      }
 240          
 241          /*******************************************************************************
 242           * 函数: Protocol_SendPosition
 243           * 功能: 发送位置信息
 244           * 参数: 无
 245           * 返回: 无
 246           ******************************************************************************/
 247          void Protocol_SendPosition(void)
 248          {
 249   1          char buffer[32];
 250   1          uint16_t x, y;
 251   1          
 252   1          x = Stepper_GetPosition(STEPPER_X_AXIS);
 253   1          y = Stepper_GetPosition(STEPPER_Y_AXIS);
 254   1          
 255   1          sprintf(buffer, "X:%u,Y:%u", x, y);
 256   1          
 257   1          UART_SendByte(STX);
 258   1          UART_SendString(RESP_OK);
 259   1          UART_SendString(buffer);
 260   1          UART_SendByte(ETX);
 261   1          UART_SendString("00");  // 简化的校验和
 262   1      }
 263          
 264          /*******************************************************************************
 265           * 函数: Protocol_SendStatus
 266           * 功能: 发送状态信息
 267           * 参数: 无
 268           * 返回: 无
 269           ******************************************************************************/
 270          void Protocol_SendStatus(void)
 271          {
 272   1          char buffer[32];
 273   1          extern volatile uint8_t system_state;
 274   1          
 275   1          sprintf(buffer, "STA:%u,PWR:%u,SPD:%u", 
 276   1                  system_state, Laser_GetPower(), stepper_speed);
 277   1          
 278   1          UART_SendByte(STX);
 279   1          UART_SendString(RESP_STA);
 280   1          UART_SendString(buffer);
 281   1          UART_SendByte(ETX);
 282   1          UART_SendString("00");  // 简化的校验和
 283   1      }

C51 COMPILATION COMPLETE.  1 WARNING(S),  2 ERROR(S)
