/*******************************************************************************
 * 步进电机控制模块头文件
 * 功能: 控制X轴和Y轴步进电机运动
 ******************************************************************************/

#ifndef __STEPPER_H__
#define __STEPPER_H__

#include "STC15W201S.h"

/*******************************************************************************
 * 宏定义
 ******************************************************************************/
#define STEPPER_X_AXIS      0       // X轴标识
#define STEPPER_Y_AXIS      1       // Y轴标识

#define STEPPER_DIR_CW      0       // 顺时针方向
#define STEPPER_DIR_CCW     1       // 逆时针方向

#define STEPPER_STATE_IDLE  0       // 空闲状态
#define STEPPER_STATE_MOVE  1       // 运动状态

/*******************************************************************************
 * 步进电机结构体定义
 ******************************************************************************/
typedef struct {
    uint16_t target_pos;            // 目标位置
    uint16_t current_pos;           // 当前位置
    uint8_t direction;              // 运动方向
    uint8_t state;                  // 运动状态
    uint16_t step_count;            // 剩余步数
    uint8_t step_phase;             // 当前步进相位
    uint16_t step_delay;            // 步进延时(控制速度)
    uint16_t step_timer;            // 步进定时器
} StepperMotor_t;

/*******************************************************************************
 * 函数声明
 ******************************************************************************/
void Stepper_Init(void);                                    // 步进电机初始化
void Stepper_MoveTo(uint8_t axis, uint16_t position);      // 移动到指定位置
void Stepper_MoveRelative(uint8_t axis, int16_t steps);    // 相对移动
void Stepper_SetSpeed(uint8_t speed);                      // 设置速度
void Stepper_Stop(uint8_t axis);                           // 停止运动
void Stepper_Process(void);                                // 步进电机处理函数
void Stepper_TimerISR(void);                              // 定时器中断服务程序
uint8_t Stepper_IsMoving(uint8_t axis);                   // 检查是否在运动
uint16_t Stepper_GetPosition(uint8_t axis);               // 获取当前位置
void Stepper_SetPosition(uint8_t axis, uint16_t position); // 设置当前位置
void Stepper_Home(uint8_t axis);                          // 回零操作
void Stepper_StepX(void);                             // X轴步进一步
void Stepper_StepY(void);                             // Y轴步进一步

/*******************************************************************************
 * 全局变量声明
 ******************************************************************************/
extern StepperMotor_t stepper_x;
extern StepperMotor_t stepper_y;
extern volatile uint8_t stepper_speed;

#endif // __STEPPER_H__
