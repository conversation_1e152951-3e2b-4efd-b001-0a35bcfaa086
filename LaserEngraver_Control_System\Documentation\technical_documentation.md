# 激光雕刻机控制系统技术文档

## 目录
1. [系统架构](#1-系统架构)
2. [硬件设计](#2-硬件设计)
3. [软件架构](#3-软件架构)
4. [通信协议](#4-通信协议)
5. [算法实现](#5-算法实现)
6. [性能指标](#6-性能指标)
7. [扩展接口](#7-扩展接口)
8. [开发环境](#8-开发环境)

## 1. 系统架构

### 1.1 整体架构
```
┌─────────────────┐    串口通信    ┌─────────────────┐
│   上位机软件    │ ◄──────────► │   单片机系统    │
│  (C# WinForms)  │   9600bps    │  (STC15W201S)   │
└─────────────────┘              └─────────────────┘
         │                                │
         │                                │
    ┌────▼────┐                      ┌────▼────┐
    │文件解析 │                      │硬件控制 │
    │路径规划 │                      │步进电机 │
    │用户界面 │                      │激光模块 │
    └─────────┘                      └─────────┘
```

### 1.2 模块划分
- **上位机模块**: 用户界面、文件处理、通信管理
- **单片机模块**: 硬件控制、运动控制、激光控制
- **通信模块**: 协议定义、数据传输、错误处理

## 2. 硬件设计

### 2.1 核心控制器
**STC15W201S单片机**
- 工作频率: 12MHz
- Flash存储: 13KB
- RAM: 1280字节
- I/O端口: 15个可用

### 2.2 引脚分配
| 功能 | 引脚 | 端口 | 说明 |
|------|------|------|------|
| 串口接收 | 9 | P3.0 | RXD |
| 串口发送 | 10 | P3.1 | TXD |
| X轴A相 | 15 | P1.0 | 步进电机控制 |
| X轴B相 | 16 | P1.1 | 步进电机控制 |
| X轴C相 | 1 | P1.2 | 步进电机控制 |
| X轴D相 | 2 | P1.3 | 步进电机控制 |
| Y轴A相 | 11 | P3.2 | 步进电机控制 |
| Y轴B相 | 12 | P3.3 | 步进电机控制 |
| Y轴C相 | 13 | P3.6 | 步进电机控制 |
| Y轴D相 | 14 | P3.7 | 步进电机控制 |
| 激光PWM | - | P2.0 | 功率控制 |
| 激光使能 | - | P2.1 | 开关控制 |

### 2.3 外围电路
- **电源电路**: 5V稳压供电
- **串口电路**: MAX232电平转换
- **步进电机驱动**: ULN2003或专用驱动芯片
- **激光驱动**: MOSFET功率开关

## 3. 软件架构

### 3.1 上位机架构
```
┌─────────────────────────────────────────┐
│                主窗体                   │
├─────────────┬─────────────┬─────────────┤
│  串口通信   │  运动控制   │  激光控制   │
├─────────────┼─────────────┼─────────────┤
│  文件解析   │  路径规划   │  状态监控   │
└─────────────┴─────────────┴─────────────┘
```

**主要类结构:**
- `MainForm`: 主窗体，用户界面
- `SerialCommunication`: 串口通信管理
- `MotionController`: 运动控制
- `LaserController`: 激光控制
- `FileParser`: 文件解析
- `PathPlanner`: 路径规划

### 3.2 单片机架构
```
┌─────────────────────────────────────────┐
│                主程序                   │
├─────────────┬─────────────┬─────────────┤
│  串口模块   │  步进模块   │  激光模块   │
├─────────────┼─────────────┼─────────────┤
│  协议模块   │  系统模块   │  定时模块   │
└─────────────┴─────────────┴─────────────┘
```

**主要模块:**
- `main.c`: 主程序入口
- `uart.c/h`: 串口通信
- `stepper.c/h`: 步进电机控制
- `laser.c/h`: 激光控制
- `protocol.c/h`: 通信协议
- `system.c/h`: 系统管理

## 4. 通信协议

### 4.1 协议格式
```
<STX><CMD><DATA><ETX><CHK>
```
- **STX**: 起始字符 (0x02)
- **CMD**: 命令字符 (1字节)
- **DATA**: 数据部分 (可变长度)
- **ETX**: 结束字符 (0x03)
- **CHK**: 校验和 (2字节十六进制)

### 4.2 命令集
| 命令 | 格式 | 功能 | 响应 |
|------|------|------|------|
| M | M<X>,<Y> | 移动到指定位置 | OK |
| R | R<dX>,<dY> | 相对移动 | OK |
| V | V<速度> | 设置速度 | OK |
| L | L<功率> | 激光开启 | OK |
| O | O | 激光关闭 | OK |
| Q | Q | 查询位置 | OK<位置> |
| T | T | 查询状态 | STA<状态> |
| Z | Z | 系统复位 | OK |
| P | P | 暂停 | OK |
| C | C | 恢复 | OK |
| S | S | 停止 | OK |

### 4.3 错误处理
| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 01 | 命令格式错误 | 重新发送 |
| 02 | 校验和错误 | 重新发送 |
| 03 | 参数超出范围 | 检查参数 |
| 04 | 系统忙碌 | 等待重试 |
| 05 | 硬件故障 | 检查硬件 |
| 06 | 未知命令 | 检查协议 |

## 5. 算法实现

### 5.1 步进电机控制算法
**四相八拍驱动:**
```c
uint8_t stepper_phase_table[8] = {
    0x01,  // 0001 - A相
    0x03,  // 0011 - A+B相
    0x02,  // 0010 - B相
    0x06,  // 0110 - B+C相
    0x04,  // 0100 - C相
    0x0C,  // 1100 - C+D相
    0x08,  // 1000 - D相
    0x09   // 1001 - D+A相
};
```

**速度控制:**
- 通过调整步进间隔时间控制速度
- 速度范围: 1-100%
- 延时计算: `delay = 200 - speed`

### 5.2 激光PWM控制
**软件PWM实现:**
```c
void Laser_TimerISR(void)
{
    laser_pwm_counter++;
    if(laser_pwm_counter >= PWM_RESOLUTION)
        laser_pwm_counter = 0;
    
    if(laser_pwm_counter < laser_power)
        LASER_PWM = 1;
    else
        LASER_PWM = 0;
}
```

### 5.3 路径优化算法
**最近邻算法:**
1. 将路径分割为独立图形
2. 从当前位置找最近的图形
3. 重新排序减少空行程
4. 平滑处理去除冗余点

## 6. 性能指标

### 6.1 运动性能
- **定位精度**: ±0.1mm
- **重复精度**: ±0.05mm
- **最大速度**: 1000mm/min
- **加速度**: 500mm/s²

### 6.2 激光性能
- **功率范围**: 0-100%
- **功率精度**: ±1%
- **PWM频率**: 1kHz
- **响应时间**: <10ms

### 6.3 通信性能
- **波特率**: 9600bps
- **命令响应**: <100ms
- **数据吞吐**: 960字节/秒
- **错误率**: <0.1%

## 7. 扩展接口

### 7.1 硬件扩展
- **限位开关**: 可接入P2.2-P2.5
- **温度传感器**: ADC接口
- **显示屏**: I2C接口
- **SD卡**: SPI接口

### 7.2 软件扩展
- **插件接口**: 支持自定义文件格式
- **脚本支持**: Lua脚本扩展
- **网络接口**: TCP/IP远程控制
- **数据库**: 作业记录和统计

## 8. 开发环境

### 8.1 单片机开发
- **IDE**: Keil uVision 5
- **编译器**: C51 Compiler
- **调试器**: STC-ISP
- **仿真器**: Proteus (可选)

### 8.2 上位机开发
- **IDE**: Visual Studio 2019/2022
- **框架**: .NET Framework 4.7.2
- **语言**: C# 8.0
- **UI框架**: Windows Forms

### 8.3 版本控制
- **工具**: Git
- **分支策略**: GitFlow
- **代码规范**: Microsoft C# Coding Conventions

### 8.4 测试工具
- **单元测试**: MSTest
- **集成测试**: 自定义测试框架
- **性能测试**: 示波器、万用表
- **协议测试**: 串口调试助手

## 9. 维护和升级

### 9.1 固件升级
- 通过STC-ISP工具升级单片机程序
- 保持向后兼容性
- 提供升级日志

### 9.2 软件更新
- 自动检查更新功能
- 增量更新机制
- 配置文件迁移

### 9.3 故障诊断
- 内置诊断功能
- 日志记录系统
- 远程诊断支持

---

**文档版本**: v1.0.0  
**最后更新**: 2025-07-20  
**维护者**: 开发团队
