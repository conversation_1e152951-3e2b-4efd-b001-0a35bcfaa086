C51 COMPILER V9.01   UART                                                                  07/20/2025 13:02:41 PAGE 1   


C51 COMPILER V9.01, COMPILATION OF MODULE UART
OBJECT MODULE PLACED IN .\Objects\uart.obj
COMPILER INVOKED BY: D:\Keil\C51\BIN\C51.EXE ..\Source\uart.c BROWSE INCDIR(..\Source) DEBUG OBJECTEXTEND PRINT(.\Listin
                    -gs\uart.lst) OBJECT(.\Objects\uart.obj)

line level    source

*** WARNING C500 IN LINE 1 OF ..\SOURCE\UART.C: LICENSE ERROR (R208: RENEW LICENSE ID CODE (LIC))

   1          /*******************************************************************************
   2           * 串口通信模块实现
   3           * 功能: 实现与上位机的串口通信
   4           ******************************************************************************/
   5          
   6          #include "uart.h"
*** ERROR C141 IN LINE 23 OF ..\SOURCE\UART.H: syntax error near ','
   7          
   8          /*******************************************************************************
   9           * 全局变量定义
  10           ******************************************************************************/
  11          volatile uint8_t uart_rx_buffer[UART_BUFFER_SIZE];
  12          volatile uint8_t uart_rx_head = 0;
  13          volatile uint8_t uart_rx_tail = 0;
  14          volatile uint8_t uart_tx_buffer[UART_BUFFER_SIZE];
  15          volatile uint8_t uart_tx_head = 0;
  16          volatile uint8_t uart_tx_tail = 0;
  17          volatile uint8_t uart_tx_busy = 0;
  18          
  19          /*******************************************************************************
  20           * 函数: UART_Init
  21           * 功能: 串口初始化
  22           * 参数: 无
  23           * 返回: 无
  24           ******************************************************************************/
  25          void UART_Init(void)
  26          {
  27   1          // 配置串口工作模式
  28   1          SCON = 0x50;    // 8位数据，可变波特率
  29   1          
  30   1          // 配置定时器1作为波特率发生器
  31   1          TMOD &= 0x0F;   // 清除定时器1模式位
  32   1          TMOD |= 0x20;   // 定时器1工作在模式2（8位自动重装载）
  33   1          
  34   1          // 计算波特率重装载值
  35   1          // 波特率 = FOSC / (32 * 12 * (256 - TH1))
  36   1          // TH1 = 256 - FOSC / (32 * 12 * 波特率)
  37   1          TH1 = 256 - (FOSC / 32 / 12 / BAUD);
  38   1          TL1 = TH1;
  39   1          
  40   1          // 启动定时器1
  41   1          TR1 = 1;
  42   1          
  43   1          // 使能串口中断
  44   1          ES = 1;
  45   1          
  46   1          // 清空缓冲区
  47   1          UART_ClearBuffer();
  48   1      }
  49          
  50          /*******************************************************************************
  51           * 函数: UART_SendByte
  52           * 功能: 发送单字节数据
C51 COMPILER V9.01   UART                                                                  07/20/2025 13:02:41 PAGE 2   

  53           * 参数: byte - 要发送的字节
  54           * 返回: 无
  55           ******************************************************************************/
  56          void UART_SendByte(uint8_t byte)
  57          {
  58   1          // 等待发送缓冲区有空间
  59   1          while(((uart_tx_head + 1) % UART_BUFFER_SIZE) == uart_tx_tail);
  60   1          
  61   1          // 将数据放入发送缓冲区
  62   1          uart_tx_buffer[uart_tx_head] = byte;
  63   1          uart_tx_head = (uart_tx_head + 1) % UART_BUFFER_SIZE;
  64   1          
  65   1          // 如果发送器空闲，启动发送
  66   1          if(!uart_tx_busy)
  67   1          {
  68   2              uart_tx_busy = 1;
  69   2              SBUF = uart_tx_buffer[uart_tx_tail];
  70   2              uart_tx_tail = (uart_tx_tail + 1) % UART_BUFFER_SIZE;
  71   2          }
  72   1      }
  73          
  74          /*******************************************************************************
  75           * 函数: UART_SendString
  76           * 功能: 发送字符串
  77           * 参数: str - 要发送的字符串
  78           * 返回: 无
  79           ******************************************************************************/
  80          void UART_SendString(char* str)
  81          {
  82   1          while(*str)
  83   1          {
  84   2              UART_SendByte(*str++);
  85   2          }
  86   1      }
  87          
  88          /*******************************************************************************
  89           * 函数: UART_SendData
  90           * 功能: 发送数据数组
  91           * 参数: data - 数据指针, len - 数据长度
  92           * 返回: 无
  93           ******************************************************************************/
  94          void UART_SendData(uint8_t* data, uint8_t len)
*** ERROR C141 IN LINE 94 OF ..\SOURCE\UART.C: syntax error near ','
  95          {
  96   1          uint8_t i;
  97   1          for(i = 0; i < len; i++)
  98   1          {
  99   2              UART_SendByte(data[i]);
*** ERROR C141 IN LINE 99 OF ..\SOURCE\UART.C: syntax error near 'data'
 100   2          }
 101   1      }
 102          
 103          /*******************************************************************************
 104           * 函数: UART_ReceiveByte
 105           * 功能: 接收单字节数据
 106           * 参数: 无
 107           * 返回: 接收到的字节
 108           ******************************************************************************/
 109          uint8_t UART_ReceiveByte(void)
 110          {
 111   1          uint8_t byte;
 112   1          
C51 COMPILER V9.01   UART                                                                  07/20/2025 13:02:41 PAGE 3   

 113   1          // 等待接收缓冲区有数据
 114   1          while(uart_rx_head == uart_rx_tail);
 115   1          
 116   1          // 从接收缓冲区取出数据
 117   1          byte = uart_rx_buffer[uart_rx_tail];
 118   1          uart_rx_tail = (uart_rx_tail + 1) % UART_BUFFER_SIZE;
 119   1          
 120   1          return byte;
 121   1      }
 122          
 123          /*******************************************************************************
 124           * 函数: UART_DataAvailable
 125           * 功能: 检查是否有数据可读
 126           * 参数: 无
 127           * 返回: 可读数据的字节数
 128           ******************************************************************************/
 129          uint8_t UART_DataAvailable(void)
 130          {
 131   1          return (uart_rx_head - uart_rx_tail + UART_BUFFER_SIZE) % UART_BUFFER_SIZE;
 132   1      }
 133          
 134          /*******************************************************************************
 135           * 函数: UART_ReceiveISR
 136           * 功能: 串口接收中断服务程序
 137           * 参数: 无
 138           * 返回: 无
 139           ******************************************************************************/
 140          void UART_ReceiveISR(void)
 141          {
 142   1          uint8_t next_head;
 143   1          
 144   1          // 计算下一个头指针位置
 145   1          next_head = (uart_rx_head + 1) % UART_BUFFER_SIZE;
 146   1          
 147   1          // 如果缓冲区未满，存储接收到的数据
 148   1          if(next_head != uart_rx_tail)
 149   1          {
 150   2              uart_rx_buffer[uart_rx_head] = SBUF;
 151   2              uart_rx_head = next_head;
 152   2          }
 153   1          // 缓冲区满时丢弃数据（可以添加错误处理）
 154   1      }
 155          
 156          /*******************************************************************************
 157           * 函数: UART_TransmitISR
 158           * 功能: 串口发送中断服务程序
 159           * 参数: 无
 160           * 返回: 无
 161           ******************************************************************************/
 162          void UART_TransmitISR(void)
 163          {
 164   1          // 如果发送缓冲区还有数据
 165   1          if(uart_tx_head != uart_tx_tail)
 166   1          {
 167   2              SBUF = uart_tx_buffer[uart_tx_tail];
 168   2              uart_tx_tail = (uart_tx_tail + 1) % UART_BUFFER_SIZE;
 169   2          }
 170   1          else
 171   1          {
 172   2              uart_tx_busy = 0;  // 发送完成
 173   2          }
 174   1      }
C51 COMPILER V9.01   UART                                                                  07/20/2025 13:02:41 PAGE 4   

 175          
 176          /*******************************************************************************
 177           * 函数: UART_ClearBuffer
 178           * 功能: 清空接收缓冲区
 179           * 参数: 无
 180           * 返回: 无
 181           ******************************************************************************/
 182          void UART_ClearBuffer(void)
 183          {
 184   1          uart_rx_head = 0;
 185   1          uart_rx_tail = 0;
 186   1          uart_tx_head = 0;
 187   1          uart_tx_tail = 0;
 188   1          uart_tx_busy = 0;
 189   1      }

C51 COMPILATION COMPLETE.  1 WARNING(S),  3 ERROR(S)
