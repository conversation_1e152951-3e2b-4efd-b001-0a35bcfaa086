"""
数据处理核心服务
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
from sklearn.ensemble import IsolationForest
import json

logger = logging.getLogger(__name__)

class BatteryDataProcessor:
    """电池数据处理器"""
    
    def __init__(self):
        self.scaler = StandardScaler()
    
    def basic_statistics(self, df):
        """基础统计分析"""
        try:
            stats_result = {
                'total_records': len(df),
                'date_range': {},
                'voltage_stats': {},
                'impedance_stats': {},
                'test_results': {},
                'batch_analysis': {},
                'channel_analysis': {}
            }
            
            # 时间范围分析
            if 'test_start_time' in df.columns:
                df['test_start_time'] = pd.to_datetime(df['test_start_time'], errors='coerce')
                valid_dates = df['test_start_time'].dropna()
                if not valid_dates.empty:
                    stats_result['date_range'] = {
                        'start_date': valid_dates.min().isoformat(),
                        'end_date': valid_dates.max().isoformat(),
                        'span_days': (valid_dates.max() - valid_dates.min()).days
                    }
            
            # 电压统计
            if 'voltage' in df.columns:
                voltage_data = pd.to_numeric(df['voltage'], errors='coerce').dropna()
                if not voltage_data.empty:
                    stats_result['voltage_stats'] = {
                        'mean': float(voltage_data.mean()),
                        'std': float(voltage_data.std()),
                        'min': float(voltage_data.min()),
                        'max': float(voltage_data.max()),
                        'median': float(voltage_data.median()),
                        'q25': float(voltage_data.quantile(0.25)),
                        'q75': float(voltage_data.quantile(0.75))
                    }
            
            # 阻抗统计
            impedance_columns = ['rs_impedance', 'rct_impedance', 'w_impedance']
            for col in impedance_columns:
                if col in df.columns:
                    impedance_data = pd.to_numeric(df[col], errors='coerce').dropna()
                    if not impedance_data.empty:
                        stats_result['impedance_stats'][col] = {
                            'mean': float(impedance_data.mean()),
                            'std': float(impedance_data.std()),
                            'min': float(impedance_data.min()),
                            'max': float(impedance_data.max()),
                            'median': float(impedance_data.median())
                        }
            
            # 测试结果统计
            if 'test_result' in df.columns:
                result_counts = df['test_result'].value_counts()
                total = len(df)
                stats_result['test_results'] = {
                    'pass_count': int(result_counts.get('合格', 0)),
                    'fail_count': int(result_counts.get('不合格', 0)),
                    'pass_rate': float(result_counts.get('合格', 0) / total * 100) if total > 0 else 0,
                    'fail_rate': float(result_counts.get('不合格', 0) / total * 100) if total > 0 else 0
                }
            
            # 批次分析
            if 'batch_number' in df.columns:
                batch_stats = df.groupby('batch_number').agg({
                    'battery_code': 'count',
                    'test_result': lambda x: (x == '合格').sum() / len(x) * 100 if len(x) > 0 else 0
                }).round(2)
                
                stats_result['batch_analysis'] = {
                    'total_batches': len(batch_stats),
                    'avg_batteries_per_batch': float(batch_stats['battery_code'].mean()),
                    'batch_details': batch_stats.to_dict('index')
                }
            
            # 通道分析
            if 'channel_number' in df.columns:
                channel_stats = df.groupby('channel_number').agg({
                    'battery_code': 'count',
                    'test_result': lambda x: (x == '合格').sum() / len(x) * 100 if len(x) > 0 else 0
                }).round(2)
                
                stats_result['channel_analysis'] = {
                    'total_channels': len(channel_stats),
                    'avg_tests_per_channel': float(channel_stats['battery_code'].mean()),
                    'channel_details': channel_stats.to_dict('index')
                }
            
            return stats_result
            
        except Exception as e:
            logger.error(f"基础统计分析错误: {str(e)}")
            raise Exception(f"统计分析失败: {str(e)}")
    
    def trend_analysis(self, df):
        """趋势分析"""
        try:
            trend_result = {
                'time_trends': {},
                'batch_trends': {},
                'correlations': {}
            }
            
            # 时间趋势分析
            if 'test_start_time' in df.columns and 'voltage' in df.columns:
                df['test_start_time'] = pd.to_datetime(df['test_start_time'], errors='coerce')
                df['voltage'] = pd.to_numeric(df['voltage'], errors='coerce')
                
                # 按日期分组分析
                daily_stats = df.groupby(df['test_start_time'].dt.date).agg({
                    'voltage': ['mean', 'std', 'count'],
                    'test_result': lambda x: (x == '合格').sum() / len(x) * 100 if len(x) > 0 else 0
                }).round(3)
                
                daily_stats.columns = ['voltage_mean', 'voltage_std', 'test_count', 'pass_rate']
                
                trend_result['time_trends'] = {
                    'daily_voltage_trend': daily_stats['voltage_mean'].to_dict(),
                    'daily_pass_rate_trend': daily_stats['pass_rate'].to_dict(),
                    'daily_test_count': daily_stats['test_count'].to_dict()
                }
            
            # 批次趋势分析
            if 'batch_number' in df.columns:
                numeric_columns = ['voltage', 'rs_impedance', 'rct_impedance', 'w_impedance']
                batch_trends = {}
                
                for col in numeric_columns:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                        batch_stats = df.groupby('batch_number')[col].agg(['mean', 'std']).round(3)
                        batch_trends[col] = batch_stats.to_dict('index')
                
                trend_result['batch_trends'] = batch_trends
            
            # 相关性分析
            numeric_columns = ['voltage', 'rs_impedance', 'rct_impedance', 'w_impedance', 'test_duration']
            available_columns = [col for col in numeric_columns if col in df.columns]
            
            if len(available_columns) >= 2:
                correlation_matrix = df[available_columns].corr().round(3)
                trend_result['correlations'] = correlation_matrix.to_dict()
            
            return trend_result
            
        except Exception as e:
            logger.error(f"趋势分析错误: {str(e)}")
            raise Exception(f"趋势分析失败: {str(e)}")
    
    def anomaly_detection(self, df):
        """异常检测"""
        try:
            anomaly_result = {
                'outliers': {},
                'anomaly_summary': {},
                'recommendations': []
            }
            
            # 数值列异常检测
            numeric_columns = ['voltage', 'rs_impedance', 'rct_impedance', 'w_impedance']
            
            for col in numeric_columns:
                if col in df.columns:
                    data = pd.to_numeric(df[col], errors='coerce').dropna()
                    
                    if len(data) > 10:  # 需要足够的数据点
                        # 使用IQR方法检测异常值
                        Q1 = data.quantile(0.25)
                        Q3 = data.quantile(0.75)
                        IQR = Q3 - Q1
                        lower_bound = Q1 - 1.5 * IQR
                        upper_bound = Q3 + 1.5 * IQR
                        
                        outliers = data[(data < lower_bound) | (data > upper_bound)]
                        
                        anomaly_result['outliers'][col] = {
                            'count': len(outliers),
                            'percentage': float(len(outliers) / len(data) * 100),
                            'lower_bound': float(lower_bound),
                            'upper_bound': float(upper_bound),
                            'outlier_values': outliers.tolist()[:20]  # 最多显示20个异常值
                        }
                        
                        # 使用Isolation Forest检测异常
                        if len(data) > 50:
                            iso_forest = IsolationForest(contamination=0.1, random_state=42)
                            anomaly_scores = iso_forest.fit_predict(data.values.reshape(-1, 1))
                            anomaly_count = np.sum(anomaly_scores == -1)
                            
                            anomaly_result['outliers'][col]['isolation_forest'] = {
                                'anomaly_count': int(anomaly_count),
                                'anomaly_percentage': float(anomaly_count / len(data) * 100)
                            }
            
            # 生成异常检测摘要
            total_outliers = sum([info['count'] for info in anomaly_result['outliers'].values()])
            total_records = len(df)
            
            anomaly_result['anomaly_summary'] = {
                'total_outliers': total_outliers,
                'outlier_percentage': float(total_outliers / total_records * 100) if total_records > 0 else 0,
                'most_problematic_column': max(anomaly_result['outliers'].items(), 
                                             key=lambda x: x[1]['percentage'])[0] if anomaly_result['outliers'] else None
            }
            
            # 生成建议
            if total_outliers > 0:
                anomaly_result['recommendations'].append("发现异常数据，建议进一步检查测试设备和流程")
                
                for col, info in anomaly_result['outliers'].items():
                    if info['percentage'] > 5:
                        anomaly_result['recommendations'].append(f"{col} 异常率较高({info['percentage']:.1f}%)，建议重点关注")
            
            return anomaly_result
            
        except Exception as e:
            logger.error(f"异常检测错误: {str(e)}")
            raise Exception(f"异常检测失败: {str(e)}")
    
    def quality_analysis(self, df):
        """质量分析"""
        try:
            quality_result = {
                'overall_quality': {},
                'batch_quality': {},
                'channel_quality': {},
                'failure_analysis': {}
            }
            
            # 整体质量分析
            if 'test_result' in df.columns:
                total_tests = len(df)
                pass_tests = len(df[df['test_result'] == '合格'])
                fail_tests = total_tests - pass_tests
                
                quality_result['overall_quality'] = {
                    'total_tests': total_tests,
                    'pass_count': pass_tests,
                    'fail_count': fail_tests,
                    'pass_rate': float(pass_tests / total_tests * 100) if total_tests > 0 else 0,
                    'fail_rate': float(fail_tests / total_tests * 100) if total_tests > 0 else 0
                }
            
            # 批次质量分析
            if 'batch_number' in df.columns and 'test_result' in df.columns:
                batch_quality = df.groupby('batch_number')['test_result'].agg([
                    'count',
                    lambda x: (x == '合格').sum(),
                    lambda x: (x == '合格').sum() / len(x) * 100
                ]).round(2)
                batch_quality.columns = ['total_tests', 'pass_count', 'pass_rate']
                
                quality_result['batch_quality'] = batch_quality.to_dict('index')
            
            # 通道质量分析
            if 'channel_number' in df.columns and 'test_result' in df.columns:
                channel_quality = df.groupby('channel_number')['test_result'].agg([
                    'count',
                    lambda x: (x == '合格').sum(),
                    lambda x: (x == '合格').sum() / len(x) * 100
                ]).round(2)
                channel_quality.columns = ['total_tests', 'pass_count', 'pass_rate']
                
                quality_result['channel_quality'] = channel_quality.to_dict('index')
            
            # 失败原因分析
            if 'failure_reason' in df.columns:
                failure_reasons = df[df['test_result'] == '不合格']['failure_reason'].value_counts()
                quality_result['failure_analysis'] = {
                    'failure_reasons': failure_reasons.to_dict(),
                    'most_common_failure': failure_reasons.index[0] if len(failure_reasons) > 0 else None
                }
            
            return quality_result
            
        except Exception as e:
            logger.error(f"质量分析错误: {str(e)}")
            raise Exception(f"质量分析失败: {str(e)}")
    
    def generate_report(self, df, analysis_types=['basic', 'trend', 'anomaly', 'quality']):
        """生成综合分析报告"""
        try:
            report = {
                'generated_at': datetime.now().isoformat(),
                'data_summary': {
                    'total_records': len(df),
                    'columns': df.columns.tolist()
                },
                'analysis_results': {}
            }
            
            if 'basic' in analysis_types:
                report['analysis_results']['basic_statistics'] = self.basic_statistics(df)
            
            if 'trend' in analysis_types:
                report['analysis_results']['trend_analysis'] = self.trend_analysis(df)
            
            if 'anomaly' in analysis_types:
                report['analysis_results']['anomaly_detection'] = self.anomaly_detection(df)
            
            if 'quality' in analysis_types:
                report['analysis_results']['quality_analysis'] = self.quality_analysis(df)
            
            return report
            
        except Exception as e:
            logger.error(f"生成报告错误: {str(e)}")
            raise Exception(f"生成报告失败: {str(e)}")
