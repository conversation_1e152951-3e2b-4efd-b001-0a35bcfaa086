/*******************************************************************************
 * 系统管理模块实现
 * 功能: 系统初始化、状态管理、安全保护
 ******************************************************************************/

#include "system.h"
#include "stepper.h"
#include "laser.h"

/*******************************************************************************
 * 外部变量声明
 ******************************************************************************/
extern volatile uint8_t system_state;

/*******************************************************************************
 * 函数: System_Init
 * 功能: 系统初始化
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void System_Init(void)
{
    // 设置系统时钟
    // STC15W201S默认使用内部RC振荡器，可以配置为12MHz
    
    // 初始化I/O端口
    P0 = 0xFF;  // 设置P0口为高电平
    P1 = 0xFF;  // 设置P1口为高电平
    P2 = 0xFF;  // 设置P2口为高电平
    P3 = 0xFF;  // 设置P3口为高电平
    
    // 设置系统状态
    system_state = SYS_IDLE;
    
    // 禁用看门狗 (如果需要可以启用)
    // WDT_CONTR = 0x00;
}

/*******************************************************************************
 * 函数: System_Reset
 * 功能: 系统复位
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void System_Reset(void)
{
    // 停止所有运动
    Stepper_Stop(STEPPER_X_AXIS);
    Stepper_Stop(STEPPER_Y_AXIS);
    
    // 关闭激光
    Laser_Off();
    
    // 重置位置到原点
    Stepper_SetPosition(STEPPER_X_AXIS, 0);
    Stepper_SetPosition(STEPPER_Y_AXIS, 0);
    
    // 设置系统状态为空闲
    system_state = SYS_IDLE;
}

/*******************************************************************************
 * 函数: System_Pause
 * 功能: 系统暂停
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void System_Pause(void)
{
    if(system_state == SYS_RUNNING)
    {
        // 停止运动但保持位置
        Stepper_Stop(STEPPER_X_AXIS);
        Stepper_Stop(STEPPER_Y_AXIS);
        
        // 关闭激光
        Laser_Off();
        
        // 设置系统状态为暂停
        system_state = SYS_PAUSE;
    }
}

/*******************************************************************************
 * 函数: System_Continue
 * 功能: 系统恢复
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void System_Continue(void)
{
    if(system_state == SYS_PAUSE)
    {
        // 恢复系统状态为运行
        system_state = SYS_RUNNING;
        
        // 注意：这里不自动恢复运动和激光，需要上位机重新发送命令
    }
}

/*******************************************************************************
 * 函数: System_Stop
 * 功能: 系统停止
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void System_Stop(void)
{
    // 立即停止所有运动
    Stepper_Stop(STEPPER_X_AXIS);
    Stepper_Stop(STEPPER_Y_AXIS);
    
    // 立即关闭激光
    Laser_Off();
    
    // 设置系统状态为空闲
    system_state = SYS_IDLE;
}

/*******************************************************************************
 * 函数: System_Monitor
 * 功能: 系统监控
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void System_Monitor(void)
{
    // 检查系统状态
    if(system_state == SYS_RUNNING)
    {
        // 检查是否有运动在进行
        if(!Stepper_IsMoving(STEPPER_X_AXIS) && !Stepper_IsMoving(STEPPER_Y_AXIS))
        {
            // 如果没有运动，可以考虑将状态改为空闲
            // system_state = SYS_IDLE;
        }
    }
    
    // 安全检查 - 这里可以添加各种安全检查
    // 例如：温度检查、限位开关检查等
    
    // 示例：简单的安全检查
    // if(某种错误条件)
    // {
    //     System_EmergencyStop();
    // }
}

/*******************************************************************************
 * 函数: System_WatchdogFeed
 * 功能: 看门狗喂狗
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void System_WatchdogFeed(void)
{
    // 如果启用了看门狗，在这里喂狗
    // WDT_CONTR |= 0x10;  // 清除看门狗
}

/*******************************************************************************
 * 函数: System_EmergencyStop
 * 功能: 紧急停止
 * 参数: 无
 * 返回: 无
 ******************************************************************************/
void System_EmergencyStop(void)
{
    // 立即停止所有运动
    Stepper_Stop(STEPPER_X_AXIS);
    Stepper_Stop(STEPPER_Y_AXIS);
    
    // 立即关闭激光
    Laser_Off();
    
    // 设置系统状态为错误
    system_state = SYS_ERROR;
    
    // 可以添加报警信号
    // 例如：蜂鸣器、LED指示等
}

/*******************************************************************************
 * 函数: System_GetState
 * 功能: 获取系统状态
 * 参数: 无
 * 返回: 系统状态
 ******************************************************************************/
uint8_t System_GetState(void)
{
    return system_state;
}
